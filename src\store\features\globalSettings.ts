import { createSlice } from "@reduxjs/toolkit";

const DEFAULT_LANGUAGES = [
  {
    label: "English",
    value: "en",
  },
  {
    label: "Polish",
    value: "pl",
  },
];

interface ILanguages {
  label: string;
  value: string;
}

export interface GlobalSettingsState {
  languages: ILanguages[];
  devMode: boolean;
  hiddenAttributes: string[];
  editorProperties: {
    menubar: string;
    toolbar: string;
  };
}

const initialState: GlobalSettingsState = {
  languages: DEFAULT_LANGUAGES,
  devMode: null,
  hiddenAttributes: [],
  editorProperties: {
    menubar: "edit insert view format tools table",
    toolbar:
      "undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image table | align lineheight | checklist numlist bullist indent outdent | emoticons | removeformat | insertIframe",
  },
};

export const globalSettingsSlice = createSlice({
  name: "globalSettings",
  initialState,
  reducers: {
    setDevMode: (state, action) => {
      state.devMode = action.payload;
    },
    setEditorProperties: (state, action) => {
      state.editorProperties = action.payload;
    },
    setHiddenAttributes: (state, action) => {
      state.hiddenAttributes = action.payload;
    },
    setLanguages: (state, action) => {
      state.languages = action.payload;
    },
  },
});

export const {
  setLanguages,
  setEditorProperties,
  setDevMode,
  setHiddenAttributes,
} = globalSettingsSlice.actions;

export default globalSettingsSlice.reducer;
