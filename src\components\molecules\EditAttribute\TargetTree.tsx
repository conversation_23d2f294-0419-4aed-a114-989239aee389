import { Tree } from "antd";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "react-query";
import {
  GET_CHILDRENS,
  GET_HEADER_MENUS,
  GET_HIERARCHY_DETAILS,
} from "../../../constants";
import { IHeaderMenus, IHierarchyData, ITreeData } from "../../../interfaces";
import { ReactComponent as DownIcon } from "../../../assets/mdi_caret.svg";
import { css } from "@linaria/core";
import { getAllNodes, getHierarchyDetails } from "../../../services/node";
import { DataNode } from "antd/es/tree";
import {
  useParentHeight,
  useTemplateActions,
} from "../../../utils/functions/customHooks";
import { getParentIds } from "../../../utils/functions/getParentIds";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

const generateTreeTable = (data: IHeaderMenus[], parentMenus) => {
  data.forEach((_headerData) => {
    if (Object.keys(_headerData.allowedChildren).length > 0) {
      parentMenus.push({
        title: _headerData.name,
        key: _headerData.id,
        isLeaf: false,
        checkable: false,
      });
    }
    if (_headerData.children.length > 0) {
      generateTreeTable(_headerData.children, parentMenus);
    }
  });
};

const TargetTreeBase = ({ onChange, val }) => {
  const queryClient = useQueryClient();
  const { getTemplateIcon } = useTemplateActions();
  const [treeData, setTreeData] = useState([]);
  const [initialLoad, setInitialLoad] = useState(true);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const { ref: containerRef, height: containerHeight } = useParentHeight();

  const menuData = queryClient.getQueryData(GET_HEADER_MENUS) as any;

  useEffect(() => {
    const parentMenus = [];
    generateTreeTable(menuData?.menu, parentMenus);
    setTreeData(parentMenus);
  }, [menuData]);

  const updateTreeData = (
    list: DataNode[],
    key: React.Key,
    children: DataNode[]
  ): DataNode[] =>
    list.map((node) => {
      if (node.key === key) {
        return {
          ...node,
          children,
        };
      }
      if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children),
        };
      }
      return node;
    });

  const onLoadData = ({ key, children }: any) =>
    new Promise<void>(async (resolve) => {
      if (children) {
        resolve();
        return;
      }

      try {
        const cacheData = queryClient.getQueryData([
          GET_CHILDRENS,
          key.toString(),
        ]);
        let data = null;
        if (cacheData) {
          data = cacheData;
        } else {
          data = await getAllNodes(key);
          queryClient.setQueryData([GET_CHILDRENS, key.toString()], data);
        }

        const newDatas = [];

        data?.forEach((value: ITreeData) => {
          newDatas.push({
            key: value.id,
            title: value.name,

            icon: getTemplateIcon(value.templateId),
            isLeaf: value?.last,
          });
        });
        setTreeData(updateTreeData(treeData, key, newDatas));
        resolve();
      } catch (e) {
        resolve();
      }
    });

  const handleCheck = (_, event) => {
    const checkedKey = event.node.key;
    if (val == checkedKey) {
      onChange(null);
    } else {
      onChange(event.node.key);
    }
  };

  useQuery([GET_HIERARCHY_DETAILS, val], () => getHierarchyDetails(val), {
    enabled: !!val && initialLoad,
    onSuccess: (data: IHierarchyData) => {
      const parentKeys = getParentIds(data.path);
      if (parentKeys.length > 0) {
        setExpandedKeys(parentKeys);
      }
      setInitialLoad(false);
    },
  });

  return (
    <div ref={containerRef} className="tree-container">
      <Tree
        className={treeStyles}
        showLine
        checkable
        checkStrictly
        showIcon
        virtual
        height={containerHeight}
        checkedKeys={val ? [val] : []}
        onCheck={handleCheck}
        loadData={onLoadData}
        treeData={treeData}
        expandedKeys={expandedKeys}
        onExpand={(keys) => setExpandedKeys(keys)}
        switcherIcon={(val) => {
          return (
            <DownIcon
              style={{
                transform: val.expanded ? `rotate(0deg)` : "rotate(-90deg)",
              }}
            />
          );
        }}
      />
    </div>
  );
};

const TargetTree = withErrorBoundary(
  React.memo(TargetTreeBase),
  "error.generic"
);

export { TargetTree };

const treeStyles = css`
  & .ant-tree-switcher svg {
    width: 20px;
  }

  & .ant-tree-iconEle img {
    object-fit: contain;
    width: 18px;
    height: 18px;
  }
  & .ant-tree-title {
    color: #084375;
  }
`;
