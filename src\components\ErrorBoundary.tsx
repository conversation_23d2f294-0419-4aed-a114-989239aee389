import React from "react";
import { ErrorFallback } from "./ErrorFallback";

interface ErrorBoundaryProps {
  fallbackKey?: string;
  children: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch() {
    // Optionally log error to an error reporting service
    // Send error logs to the server
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback
          messageKey={this.props.fallbackKey || "error.generic"}
          error={this.state.error}
        />
      );
    }
    return this.props.children;
  }
}
