.lexical-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  position: relative;
  line-height: 1.7;
  font-weight: 400;
}

.lexical-editor-container:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.lexical-editor-inner {
  background: #fff;
  position: relative;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.lexical-content-editable {
  min-height: 150px;
  max-width: 100%;
  border: 0;
  font-size: 15px;
  display: block;
  position: relative;
  tab-size: 1;
  outline: 0;
  padding: 8px 12px;
  caret-color: rgb(5, 5, 5);
}

.lexical-placeholder {
  color: #999;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 8px;
  left: 12px;
  font-size: 15px;
  user-select: none;
  display: inline-block;
  pointer-events: none;
}

.ltr {
  text-align: left;
}

.rtl {
  text-align: right;
}

.editor-paragraph {
  margin: 0;
  position: relative;
}

.editor-quote {
  margin: 0;
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  color: rgb(101, 103, 107);
  border-left-color: rgb(206, 208, 212);
  border-left-width: 4px;
  border-left-style: solid;
  padding-left: 16px;
}

.editor-heading-h1 {
  font-size: 24px;
  color: rgb(5, 5, 5);
  font-weight: 400;
  margin: 0;
}

.editor-heading-h2 {
  font-size: 20px;
  color: rgb(101, 103, 107);
  font-weight: 700;
  margin: 0;
}

.editor-heading-h3 {
  font-size: 18px;
  margin: 0;
  font-weight: 600;
}

.editor-heading-h4 {
  font-size: 16px;
  margin: 0;
  font-weight: 600;
}

.editor-heading-h5 {
  font-size: 14px;
  margin: 0;
  font-weight: 600;
}

.editor-heading-h6 {
  font-size: 12px;
  margin: 0;
  font-weight: 600;
}

.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-text-code {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}

.editor-link {
  color: rgb(33, 111, 219);
  text-decoration: none;
}

.editor-link:hover {
  text-decoration: underline;
}

.editor-list-ol {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-list-ul {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-listitem {
  margin: 8px 32px 8px 32px;
}

.editor-nested-listitem {
  list-style-type: none;
}

.editor-code {
  background-color: rgb(240, 242, 245);
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 52px;
  line-height: 1.53;
  font-size: 13px;
  margin: 0;
  margin-top: 8px;
  margin-bottom: 8px;
  tab-size: 2;
  overflow-x: auto;
  position: relative;
}

.editor-table {
  border-collapse: collapse;
  border-spacing: 0;
  max-width: 100%;
  overflow-y: scroll;
  table-layout: fixed;
  width: 100%;
}

.editor-tableCell,
.editor-tableCellHeader {
  border: 1px solid #bbb;
  width: 75px;
  min-width: 75px;
  vertical-align: top;
  text-align: start;
  padding: 6px 8px;
  position: relative;
  outline: none;
}

.editor-tableCellHeader {
  background-color: #f2f3f5;
}

/* Code highlighting */
.editor-tokenComment {
  color: slategray;
}

.editor-tokenPunctuation {
  color: #999;
}

.editor-tokenProperty {
  color: #905;
}

.editor-tokenSelector {
  color: #690;
}

.editor-tokenOperator {
  color: #9a6e3a;
}

.editor-tokenAttr {
  color: #07a;
}

.editor-tokenVariable {
  color: #e90;
}

.editor-tokenFunction {
  color: #dd4a68;
}
