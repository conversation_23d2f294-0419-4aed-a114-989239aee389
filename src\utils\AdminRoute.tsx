import { useSelector } from "react-redux";
import { Navigate } from "react-router-dom";
import { RootState } from "../store";

export const AdminRoute = ({ children }) => {
  const { authenticated, role } = useSelector((root: RootState) => root.auth);

  if (authenticated === null) {
    return;
  }
  if (!authenticated) {
    return <Navigate to="/login" />;
  }

  if (role !== "admin") {
    return <Navigate to="/" />;
  }

  return children;
};
