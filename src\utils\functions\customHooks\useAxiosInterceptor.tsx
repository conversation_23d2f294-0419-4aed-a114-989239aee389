import { useEffect } from "react";
import { API } from "../../api";
import { useDispatch } from "react-redux";
import { setAuthenticated } from "../../../store/features/auth";
import { useNavigate } from "react-router-dom";
import { useQueryClient } from "react-query";

const useAxiosInterceptor = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  useEffect(() => {
    const requestInterceptor = API.interceptors.request.use(
      async (config) => {
        const token = localStorage.getItem("token");
        if (token) {
          config.headers["CDO-TOKEN"] = token;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    const responseInterceptor = API.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error?.status === 401) {
          queryClient.clear();
          dispatch(setAuthenticated(false));
          localStorage.removeItem("token");
          navigate("/login");
        }
        return Promise.reject(error);
      }
    );

    return () => {
      API.interceptors.request.eject(requestInterceptor);
      API.interceptors.response.eject(responseInterceptor);
    };
  }, []);
};

export { useAxiosInterceptor };
