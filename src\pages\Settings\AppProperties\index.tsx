import { styled } from "@linaria/react";
import { useEffect, useState } from "react";
import { BreadCrumb, MyTable } from "../../../components";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../../store/features";
import { GET_APP_PROPERTIES, GET_LOCAL_SETTINGS_KEY } from "../../../constants";
import { IAppProperties, ILocalSettings } from "../../../interfaces";
import { But<PERSON>, Flex } from "antd";
import { saveLocalSettings, updateAppProperties } from "../../../services";
import { useNotification } from "../../../utils/functions/customHooks";
import { EditOutlined, LoadingOutlined } from "@ant-design/icons";
import { RootState } from "../../../store";
import { Divider } from "primereact/divider";
import { CustomizeEditor } from "./CustomizeEditor";
import { Editor } from "@tinymce/tinymce-react";

const API = "jq81879uordxhu0ggcajdxc24pig6pim3z7qk5m7nzxszrf7";

const EDITOR_PROPERTIES = [
  "allowed.htmltags.menubar",
  "allowed.htmltags.toolbar",
];

const AppProperties = () => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [rows, setRows] = useState([]);
  const [editorKey, setEditorKey] = useState(0);
  const [options, setOptions] = useState({
    menubar: "edit insert view format tools table",
    toolbar:
      "undo redo blocks fontfamily fontsize bold italic underline strikethrough link image table align lineheight checklist numlist bullist indent outdent emoticons removeformat insertIframe",
  });

  const [columns, setColumns] = useState([]);
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);
  const [columnsRequest, setColumnsRequest] = useState([]);
  const [isPropertiesEdited, setPropertiesEdited] = useState(false);
  const [isTableEdited, setIsTableEdited] = useState(false);

  const [customizeEditorOpen, setCustomizeEditor] = useState(false);
  const [resetTrigger, setResetTrigger] = useState(0);

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs(breadcrumb));
  }, []);

  const appProperties = queryClient.getQueryData(
    GET_APP_PROPERTIES
  ) as IAppProperties[];

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const isFetching = queryClient.isFetching(GET_APP_PROPERTIES);

  const devMode = useSelector(
    (state: RootState) => state.globalSettings.devMode
  );
  const { contextHolder, showErrorNotification, showSuccessNotification } =
    useNotification();

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }

    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.appPropertiesTable &&
      localSettingsData?.body[0]?.value?.appPropertiesTable?.columns.length > 0
    ) {
      if (localSettingsData?.body[0]?.value?.appPropertiesTable?.columns) {
        const pinned =
          localSettingsData?.body[0]?.value?.appPropertiesTable?.pinned || [];
        const sort =
          localSettingsData?.body[0]?.value?.appPropertiesTable?.sort || [];

        const allColumns = [];
        localSettingsData.body[0].value.appPropertiesTable.columns?.forEach(
          (column) => {
            const index = COLUMNS.findIndex((item) => item.field === column);
            allColumns.push({
              ...COLUMNS[index],
              pinned: pinned?.includes(column) ? "left" : null,
              sort: sort?.find((val) => val.colId === column)?.sort || null,
            });
          }
        );
        setColumnsRequest(
          localSettingsData?.body[0]?.value?.appPropertiesTable?.columns
        );
        setPinned(
          localSettingsData?.body[0]?.value?.appPropertiesTable?.pinned
        );
        setSort(localSettingsData?.body[0]?.value?.appPropertiesTable?.sort);
        setFilters(
          localSettingsData?.body[0]?.value?.appPropertiesTable?.filters
        );
        setColumns(allColumns);
      }
    } else {
      setColumns(COLUMNS);
      setColumnsRequest(COLUMNS?.map((col) => col.field));
    }
  }, [localSettingsData, resetTrigger]);

  useEffect(() => {
    if (appProperties) {
      const data = [];
      appProperties?.forEach((property: IAppProperties) => {
        if (!EDITOR_PROPERTIES.includes(property?.key)) {
          data.push({
            ...property,
            id: property.key,
            width: 250,
            isHidden: false,
          });
        }
      });
      const menubar = appProperties?.find(
        (_prop) => _prop.key === "allowed.htmltags.menubar"
      )?.value;
      const toolbar = appProperties?.find(
        (_prop) => _prop.key === "allowed.htmltags.toolbar"
      )?.value;
      if (menubar || toolbar) {
        setOptions({
          menubar: menubar,
          toolbar: toolbar,
        });
        setEditorKey((key) => key + 1);
      }

      setRows([...data]);
    }
  }, [devMode, appProperties]);

  const tableMutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const mutation = useMutation(updateAppProperties, {
    onSuccess: () => {
      showSuccessNotification("Application properties updated successfully!");
      dispatch(setMask(false));
      const rowsRequest = [...rows];
      rowsRequest.forEach((_row) => {
        delete _row["isHidden"];
        delete _row["width"];
        delete _row["id"];
      });
      queryClient.setQueryData(GET_APP_PROPERTIES, rowsRequest);
    },
    onError: () => {
      dispatch(setMask(false));
      showErrorNotification("Unable to save data!");
    },
  });

  const handleSave = () => {
    if (isTableEdited) {
      const request = {
        value: {
          ...(localSettingsData?.body
            ? localSettingsData?.body[0]?.value || {}
            : {}),
          appPropertiesTable: {
            columns: columnsRequest,
            filters: filters,
            sort: sort,
            pinned: pinned,
          },
        },
      };
      tableMutation.mutate(request);
    }
    if (isPropertiesEdited) {
      const rowsRequest = [...rows];
      rowsRequest.forEach((_row) => {
        delete _row["isHidden"];
        delete _row["width"];
        delete _row["id"];
      });
      rowsRequest.push({
        key: "allowed.htmltags.menubar",
        value: options.menubar,
      });
      rowsRequest.push({
        key: "allowed.htmltags.toolbar",
        value: options.toolbar,
      });

      mutation.mutate(rowsRequest);
    }
  };

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const detectChange = () => {
    if (!mask) {
      dispatch(setMask(true));
    }
  };

  const handleCancel = () => {
    setIsTableEdited(false);
    setPropertiesEdited(false);

    setResetTrigger((trigger) => trigger + 1);
    setTimeout(() => {
      dispatch(setMask(false));
    }, 200);

    if (appProperties) {
      const data = [];
      appProperties?.forEach((property: IAppProperties) => {
        if (!EDITOR_PROPERTIES.includes(property?.key)) {
          data.push({
            ...property,
            id: property.key,
            key: property.key,
            value: property.value,
            description: property.description,
            width: 250,
          });
        }
      });
      setRows([...data]);
    }
  };

  return (
    <Wrapper>
      {contextHolder}
      <BreadCrumb
        extra={
          mask && (
            <Flex gap={10}>
              <Button
                className="breadcrumb-button cancel-button"
                type="primary"
                onClick={handleCancel}
              >
                {t("Cancel")}
              </Button>
              <Button
                className="breadcrumb-button save-button"
                type="primary"
                onClick={handleSave}
                loading={mutation.isLoading || tableMutation.isLoading}
              >
                {t("Save")}
              </Button>
            </Flex>
          )
        }
      />

      {isFetching ? (
        <div className="loader">
          <LoadingOutlined />
        </div>
      ) : (
        <div
          className="content"
          style={{ border: mask ? "1px solid red" : "none", flex: 1 }}
        >
          <MyTable
            columns={columns}
            data={rows}
            detectChange={() => {
              detectChange();
              setIsTableEdited(true);
            }}
            resetTrigger={resetTrigger}
            editable
            loading={mutation.isLoading}
            excelFileName="properties"
            onRowsEdit={(values) => {
              const editIndex = rows?.findIndex(
                (data) => data?.key === values?.key
              );
              const newRows = [...rows];
              newRows[editIndex] = values;

              setRows([...newRows]);
              detectChange();
              setPropertiesEdited(true);
            }}
            setPinned={setPinned}
            setColumnsRequest={setColumnsRequest}
            setFilters={setFilters}
            setSort={setSort}
            initialFilters={
              localSettingsData?.body[0]?.value?.appPropertiesTable?.filters ||
              {}
            }
          />
          <Divider />

          <div className="card">
            <h5>
              {t("Editor Setup")}{" "}
              <div className="edit" onClick={() => setCustomizeEditor(true)}>
                <EditOutlined /> {t("Edit")}
              </div>{" "}
            </h5>

            <div className="content">
              <Editor
                apiKey={API}
                key={editorKey}
                init={{
                  branding: false,
                  menubar: options.menubar,
                  plugins:
                    "preview charmap emoticons image link lists searchreplace table wordcount",
                  toolbar: options.toolbar,
                  elementpath: false,
                  extended_valid_elements:
                    "iframe[src|frameborder|style|scrolling|class|width|height|name|align]",
                  content_style:
                    "tbody, tr, td {border-style: inherit !important;}  p { margin: 0 }",
                }}
                value={"This is preview!"}
                disabled
              />
            </div>
          </div>
        </div>
      )}

      <CustomizeEditor
        options={options}
        setOptions={setOptions}
        visible={customizeEditorOpen}
        onHide={() => {
          setCustomizeEditor(false);
          setEditorKey((key) => key + 1);
        }}
      />
    </Wrapper>
  );
};

const breadcrumb = [
  {
    title: "Settings",
    to: "/settings",
  },
  {
    title: "Application properties",
    to: "/settings/app-properties",
  },
];

export default AppProperties;

const COLUMNS = [
  {
    headerName: "Settings",
    field: "key",
    minWidth: 250,
    flex: 1,
  },
  {
    headerName: "Value",
    field: "value",
    minWidth: 250,
    flex: 1,
    editable: true,
    cellEditor: "agTextCellEditor",
  },
  {
    headerName: "Description",
    field: "description",
    minWidth: 250,
    flex: 1,
    editable: true,
    cellEditor: "agTextCellEditor",
  },
  {
    headerName: "",
    field: "actions",
    isEditActions: true,
  },
];

const Wrapper = styled.div`
  flex: 1;
  overflow: hidden;
  background-color: #fff !important;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;

  & .content {
    padding: 16px;
  }

  & .my-table {
    height: fit-content;
  }
  & .card {
    padding: 5px 0px;

    & .content {
      padding-left: 0px;
      padding-right: 0px;
    }

    & h5 {
      display: flex;
      margin-bottom: 10px;
      justify-content: space-between;
      font-size: 15px;
      font-weight: 500;
      color: var(--color-text);

      & .edit {
        font-size: 13px;
        cursor: pointer;
      }
    }
  }
  & .p-datatable-wrapper {
    margin: 10px 20px;
  }
  & .loader {
    font-size: 30px;
    min-height: 400px;
    display: flex;
    justify-content: center;
  }
  & .breadcrumb-button {
    font-size: 13px;
    height: 24px;
    padding: 0px 15px;
    border-radius: 3px;
  }

  & > div:first-child {
    position: sticky;
    top: 0px;
    z-index: 1;
  }
`;
