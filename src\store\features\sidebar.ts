import { createSlice } from "@reduxjs/toolkit";
import { IBreadcrumbs, INodes } from "../../interfaces";

type IFocusedNode = {
  number: number;
};

export interface SidebarState {
  menuCollapsed: boolean;
  mask: boolean;
  expandedKeys: number[];
  focusedNode: IFocusedNode;
  selected: {
    keys: number[];
    info: INodes[];
  };
  displaySaveButton: boolean;
  attributeMask: boolean;
  breadcrumb: IBreadcrumbs[];
  moveToFocused: boolean;
  movePayload: {
    string?: string;
  };
}

const initialState: SidebarState = {
  menuCollapsed: true,
  mask: false,
  expandedKeys: [],
  breadcrumb: [],
  focusedNode: <IFocusedNode>{},
  selected: {
    keys: [],
    info: [],
  },
  displaySaveButton: false,
  attributeMask: false,
  moveToFocused: false,
  movePayload: {},
};

export const sidebarSlice = createSlice({
  name: "sidebar",
  initialState,
  reducers: {
    setMovePayload: (state, action) => {
      state.movePayload = action.payload;
    },
    setFocusedNode: (state, action) => {
      state.focusedNode = action.payload;
    },
    setSelected: (state, action) => {
      state.selected = action.payload;
    },
    setDisplaySaveButton: (state, action) => {
      state.displaySaveButton = action.payload;
    },
    setAttributeMask: (state, action) => {
      state.attributeMask = action.payload;
    },
    setBreadcrumb: (state, action) => {
      state.breadcrumb = action.payload;
    },
    setMoveToFocused: (state, action) => {
      state.moveToFocused = action.payload;
    },
    addToExpandedKeys: (state, action) => {
      state.expandedKeys = [...state.expandedKeys, ...action.payload];
    },
    setExpandedKeys: (state, action) => {
      state.expandedKeys = action.payload;
    },
    setMask: (state, action) => {
      state.mask = action.payload;
    },
    setMenuCollapsed: (state, action) => {
      state.menuCollapsed = action.payload;
    },
  },
});

export const {
  setMenuCollapsed,
  setMask,
  setExpandedKeys,
  addToExpandedKeys,
  setDisplaySaveButton,
  setAttributeMask,
  setSelected,
  setFocusedNode,
  setBreadcrumb,
  setMoveToFocused,
  setMovePayload,
} = sidebarSlice.actions;

export default sidebarSlice.reducer;
