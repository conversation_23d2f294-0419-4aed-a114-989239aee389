import React, { useCallback, useEffect, useState } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $getSelection,
  $isRangeSelection,
  CAN_REDO_COMMAND,
  CAN_UNDO_COMMAND,
  FORMAT_TEXT_COMMAND,
  REDO_COMMAND,
  SELECTION_CHANGE_COMMAND,
  UNDO_COMMAND,
} from 'lexical';
import {
  $isListNode,
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
  ListNode,
  REMOVE_LIST_COMMAND,
} from '@lexical/list';
import { $isHeadingNode } from '@lexical/rich-text';
import { $findMatchingParent, mergeRegister } from '@lexical/utils';
import { TOGGLE_LINK_COMMAND } from '@lexical/link';
import { Button } from 'antd';
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  RedoOutlined,
  UndoOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  LinkOutlined,
} from '@ant-design/icons';
import './ToolbarPlugin.css';

const LowPriority = 1;

interface ToolbarPluginProps {
  minimal?: boolean;
  onInsertIframe?: () => void;
  editorProperties?: any;
}

export function ToolbarPlugin({ minimal, onInsertIframe }: ToolbarPluginProps) {
  const [editor] = useLexicalComposerContext();
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [isStrikethrough, setIsStrikethrough] = useState(false);
  const [blockType, setBlockType] = useState('paragraph');

  const updateToolbar = useCallback(() => {
    try {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        // Update text format
        setIsBold(selection.hasFormat('bold'));
        setIsItalic(selection.hasFormat('italic'));
        setIsUnderline(selection.hasFormat('underline'));
        setIsStrikethrough(selection.hasFormat('strikethrough'));

        // Update block type - simplified to avoid crashes
        const anchorNode = selection.anchor.getNode();
        if (anchorNode) {
          const element = anchorNode.getTopLevelElement();
          if (element) {
            if ($isListNode(element)) {
              setBlockType(element.getListType());
            } else if ($isHeadingNode(element)) {
              setBlockType(element.getTag());
            } else {
              setBlockType('paragraph');
            }
          }
        }
      }
    } catch (error) {
      console.error('Error updating toolbar:', error);
    }
  }, [editor]);

  useEffect(() => {
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updateToolbar();
        });
      }),
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        (_payload, _newEditor) => {
          updateToolbar();
          return false;
        },
        LowPriority
      ),
      editor.registerCommand(
        CAN_UNDO_COMMAND,
        (payload) => {
          setCanUndo(payload);
          return false;
        },
        LowPriority
      ),
      editor.registerCommand(
        CAN_REDO_COMMAND,
        (payload) => {
          setCanRedo(payload);
          return false;
        },
        LowPriority
      )
    );
  }, [editor, updateToolbar]);

  const formatBold = () => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');
  };

  const formatItalic = () => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');
  };

  const formatUnderline = () => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline');
  };

  const formatStrikethrough = () => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'strikethrough');
  };

  const formatBulletList = () => {
    if (blockType !== 'bullet') {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }
  };

  const formatNumberedList = () => {
    if (blockType !== 'number') {
      editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }
  };

  const insertLink = () => {
    if (blockType !== 'link') {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, 'https://');
    } else {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, null);
    }
  };

  if (minimal) {
    return (
      <div className="toolbar">
        <Button
          size="small"
          disabled={!canUndo}
          onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
          icon={<UndoOutlined />}
          title="Undo"
        />
        <Button
          size="small"
          disabled={!canRedo}
          onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
          icon={<RedoOutlined />}
          title="Redo"
        />
        <div className="divider" />
        <Button
          size="small"
          type={isBold ? 'primary' : 'default'}
          onClick={formatBold}
          icon={<BoldOutlined />}
          title="Bold"
        />
        <Button
          size="small"
          type={isItalic ? 'primary' : 'default'}
          onClick={formatItalic}
          icon={<ItalicOutlined />}
          title="Italic"
        />
        <Button
          size="small"
          type={isUnderline ? 'primary' : 'default'}
          onClick={formatUnderline}
          icon={<UnderlineOutlined />}
          title="Underline"
        />
        <Button
          size="small"
          type={isStrikethrough ? 'primary' : 'default'}
          onClick={formatStrikethrough}
          icon={<StrikethroughOutlined />}
          title="Strikethrough"
        />
      </div>
    );
  }

  return (
    <div className="toolbar">
      <Button
        size="small"
        disabled={!canUndo}
        onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
        icon={<UndoOutlined />}
        title="Undo"
      />
      <Button
        size="small"
        disabled={!canRedo}
        onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
        icon={<RedoOutlined />}
        title="Redo"
      />
      <div className="divider" />
      <Button
        size="small"
        type={isBold ? 'primary' : 'default'}
        onClick={formatBold}
        icon={<BoldOutlined />}
        title="Bold"
      />
      <Button
        size="small"
        type={isItalic ? 'primary' : 'default'}
        onClick={formatItalic}
        icon={<ItalicOutlined />}
        title="Italic"
      />
      <Button
        size="small"
        type={isUnderline ? 'primary' : 'default'}
        onClick={formatUnderline}
        icon={<UnderlineOutlined />}
        title="Underline"
      />
      <Button
        size="small"
        type={isStrikethrough ? 'primary' : 'default'}
        onClick={formatStrikethrough}
        icon={<StrikethroughOutlined />}
        title="Strikethrough"
      />
      <div className="divider" />
      <Button
        size="small"
        type={blockType === 'bullet' ? 'primary' : 'default'}
        onClick={formatBulletList}
        icon={<UnorderedListOutlined />}
        title="Bullet List"
      />
      <Button
        size="small"
        type={blockType === 'number' ? 'primary' : 'default'}
        onClick={formatNumberedList}
        icon={<OrderedListOutlined />}
        title="Numbered List"
      />
      <div className="divider" />
      <Button
        size="small"
        onClick={insertLink}
        icon={<LinkOutlined />}
        title="Insert Link"
      />
      {onInsertIframe && (
        <Button
          size="small"
          onClick={onInsertIframe}
          title="Insert Iframe"
        >
          Iframe
        </Button>
      )}
    </div>
  );
}
