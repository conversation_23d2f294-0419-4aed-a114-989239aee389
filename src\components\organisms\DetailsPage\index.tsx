import { styled } from "@linaria/react";
import { theme } from "../../../utils/theme";
import { useEffect, useState } from "react";
import {
  BottomNavigationDrawer,
  BreadCrumb,
  ChildrenTableView,
  ISidebarAction,
  Sidebar,
  WorkspaceContainer,
} from "../..";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { Button, Tooltip } from "antd";
import { useMutation, useQueryClient } from "react-query";
import { addAttributes, addNodeService } from "../../../services/node";
import { searchRecursivelyByID } from "../../../utils/functions/sidebarActions";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  setHideAllPathNames,
  setParentBreadcrumbs,
  setRefreshBreadcrumbs,
  setTempBreadcrumbs,
} from "../../../store/features/breadcrumbs";
import {
  setDisplaySaveButton,
  setFocusedNode,
  setMask,
  setSelected,
} from "../../../store/features/sidebar";
import { useTranslation } from "react-i18next";
import { useFunctions } from "./useFunctions";
import { IAttributes, INodeDetails, ITreeData } from "../../../interfaces";
import {
  ATTRIBUTES_WITH_NO_MANDATORY,
  AUTHOR_USERS_TEMPLATE_ID,
  GET_CHILDRENS,
  GET_COUNTERS,
  GET_GRAPH_DATA,
  GET_HISTORY_DATA,
  GET_LOGS_DATA,
  GET_NODE_ATTRIBUTES_DETAILS,
  MAX_SUPPORTED_POSITIVE_VALUE,
  MIN_SUPPORTED_NEGATIVE_VALUE,
  PERMISSION_PERSON_ID,
  TEMP_GROUPING_NODE_ID,
  USER_GROUP_ID,
  USER_TEMPLATE_ID,
} from "../../../constants";
import {
  useNotification,
  useFlags,
} from "../../../utils/functions/customHooks";
import { useSidebarActions } from "../../../utils/functions/customHooks/useSidebarActions";
import { deepClone, useTheme } from "../../../utils";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  NodeCollapseOutlined,
  NodeExpandOutlined,
} from "@ant-design/icons";
import { withErrorBoundary } from "../../withErrorBoundary";

const DetailsPageBase = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const params = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  // states
  const [initialAttributes, setInitialAttributes] = useState([]);
  const [treeData, setTreeData] = useState([] as ITreeData[]);
  const [editingAttribute, setEditingAttribute] = useState(null);
  const [disabled, setDisabled] = useState(false);
  const [disabledInfo, setDisabledInfo] = useState("");
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [previousPath, setPreviousPath] = useState(location.pathname);
  const [editMode, setEditMode] = useState(false);
  const [isChildrensShown, setIsChildrensShown] = useState(false);
  const [attributes, setAttributes] = useState([]);
  const [mandatoryAttributes, setMandatoryAttributes] = useState([]);
  const [specialAttribute, setSpecialAttribute] = useState(false);
  const [action, setAction] = useState({
    id: "",
    key: null,
    label: "",
  } as ISidebarAction);

  // selectors
  const selected = useSelector((state: RootState) => state.sidebar.selected);
  const { selectedBottomNavbar, bottomNavbarOpen } = useSelector(
    (state: RootState) => state.navigation
  );
  const displaySaveButton = useSelector(
    (state: RootState) => state.sidebar.displaySaveButton
  );
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const focusedNode = useSelector(
    (state: RootState) => state.sidebar.focusedNode
  );
  const bottomDrawerMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );

  const hideAllPathNames = useSelector(
    (state: RootState) => state.breadcrumbs.hideAllpathNames
  );

  // custom hooks
  const { showErrorNotification, showSuccessNotification, contextHolder } =
    useNotification();
  const { updateAllowedChildrens } = useFunctions();
  const { updateNewIdRecursively } = useSidebarActions();

  const metamodel = location.pathname.includes("metamodel");
  const dataSource = location.pathname.includes("data-sources");
  const isActions = location.pathname.includes("actions");
  const roles = location.pathname.includes("roles");
  const isUsers = location.pathname.includes("users");
  const isUserGroups = location.pathname.includes("user-groups");
  const isAuthors = location.pathname.includes("authors");
  const WEB_URL =
    import.meta.env.VITE_APP_BASE_URL !== "/"
      ? location.pathname.replace(import.meta.env.VITE_APP_BASE_URL, "")
      : location.pathname;

  useEffect(() => {
    if (metamodel) {
      dispatch(
        setParentBreadcrumbs([
          {
            title: "Settings",
            to: "/settings",
          },
          {
            title: "Metamodel",
            to: "/settings/metamodel/-1",
            displayTemplatesOption: true,
            id: -1,
          },
        ])
      );
    } else if (dataSource) {
      dispatch(
        setParentBreadcrumbs([
          {
            title: "Settings",
            to: "/settings",
          },
          {
            title: "Data Sources",
            to: "/settings/data-sources/-2",
            displayTemplatesOption: true,
            id: -2,
          },
        ])
      );
    } else if (isUsers) {
      dispatch(
        setParentBreadcrumbs([
          {
            title: "Settings",
            to: "/settings",
          },
          {
            title: "Users",
            to: `/settings/users/${PERMISSION_PERSON_ID}`,
            displayTemplatesOption: true,
            id: Number(PERMISSION_PERSON_ID),
          },
        ])
      );
    } else if (isUserGroups) {
      dispatch(
        setParentBreadcrumbs([
          {
            title: "Settings",
            to: "/settings",
          },
          {
            title: "User Groups",
            to: `/settings/user-groups/${USER_GROUP_ID}`,
            displayTemplatesOption: true,
            id: Number(USER_GROUP_ID),
          },
        ])
      );
    } else if (roles) {
      dispatch(
        setParentBreadcrumbs([
          {
            title: "Settings",
            to: "/settings",
          },
          {
            title: "Roles",
            to: "/settings/roles/-5",
            displayTemplatesOption: true,
            id: -5,
          },
        ])
      );
    } else if (isActions) {
      dispatch(
        setParentBreadcrumbs([
          {
            title: "Settings",
            to: "/settings",
          },
          {
            title: "Actions",
            to: "/settings/actions/-6",
            displayTemplatesOption: true,
            id: -6,
          },
        ])
      );
    } else if (isAuthors) {
      dispatch(
        setParentBreadcrumbs([
          {
            title: "Authors",
            to: "/authors",
          },
          {
            title: "Authors",
            to: `/authors/${AUTHOR_USERS_TEMPLATE_ID}`,
            displayTemplatesOption: true,
            id: Number(AUTHOR_USERS_TEMPLATE_ID),
          },
        ])
      );
    }

    if (searchParams.get("draft")) {
      navigate(WEB_URL);
    }
  }, []);

  useEffect(() => {
    if (previousPath !== location.pathname) {
      setPreviousPath(location.pathname);
    }
  }, [location.pathname]);

  const updateNewId = (oldRandomId, newId) => {
    const selectedNode = searchRecursivelyByID(treeData, oldRandomId);
    const newTreeData = [...treeData];
    updateNewIdRecursively(newTreeData, oldRandomId, newId);
    setTreeData(newTreeData);

    if (selectedNode) {
      const previousData =
        (queryClient.getQueryData([
          GET_CHILDRENS,
          selectedNode?.parentId.toString(),
        ]) as any[]) || [];

      previousData.unshift({
        body: [],
        id: newId,
        last: true,
        name: selectedNode?.name,
        parentId: selectedNode?.parentId,
        templateId: selectedNode?.templateId,
      });
      queryClient.setQueryData(
        [GET_CHILDRENS, selectedNode.parentId.toString()],
        previousData
      );

      dispatch(
        setSelected({
          keys: [newId],
          info: [
            {
              id: newId,
              isAsset: false,
              name: selectedNode?.name,
              parentId: selectedNode?.parentId,
              templateId: selectedNode?.templateId,
              body: selectedNode?.body,
              isLeaf: selectedNode?.isLeaf,
            },
          ],
        })
      );
      const focusedNodes = { ...focusedNode };
      focusedNodes[params?.nodeId] = newId;
      dispatch(setFocusedNode(focusedNodes));
    }

    setTimeout(() => {
      navigate(`${WEB_URL}?nodeId=${newId}`);
      dispatch(setMask(false));
      dispatch(setDisplaySaveButton(false));
    }, 500);
  };

  const addNodeMutation = useMutation(addNodeService, {
    onSuccess: (newNodeId) => {
      updateNewId(searchParams.get("nodeId"), newNodeId);

      setSpecialAttribute(false);
      showSuccessNotification(
        metamodel
          ? t("Template added successfully!")
          : t("Node added successfully!")
      );
      setEditingAttribute(null);
      setEditMode(false);
      dispatch(setMask(false));
      queryClient.invalidateQueries([
        "get-history",
        searchParams.get("nodeId"),
      ]);
      // if (
      //   searchParams.get("template") &&
      //   searchParams.get("template") === COMMENT_TEMPLATE_ID.toString()
      // ) {
      //   queryClient.invalidateQueries(GET_COMMENTS);
      // }
    },
    onError: (error: any) => {
      // The value user has entered being returned from server in this prop
      const enteredValue = error?.data?.params?.value;

      // Error message to be displayed
      const errorMessage = enteredValue
        ? `${t("These name characters are prohibited:")} ${enteredValue}`
        : error?.data?.details;

      showErrorNotification(errorMessage || "Unable to save node!", "", false);
    },
  });

  const mutation = useMutation(addAttributes, {
    onSuccess: () => {
      dispatch(setDisplaySaveButton(false));
      if (metamodel) {
        showSuccessNotification("Template updated successfully!");
      } else {
        showSuccessNotification("Node updated successfully!");
      }

      setEditingAttribute(null);
      setEditMode(false);
      dispatch(setMask(false));
      if (
        selectedBottomNavbar === "test-execution" ||
        selectedBottomNavbar === "test-chart"
      ) {
        queryClient.invalidateQueries([
          GET_NODE_ATTRIBUTES_DETAILS,
          searchParams.get("nodeId"),
        ]);
      }
      queryClient.invalidateQueries([GET_COUNTERS, searchParams.get("nodeId")]);
      queryClient.invalidateQueries([
        GET_HISTORY_DATA,
        searchParams.get("nodeId"),
      ]);
      queryClient.invalidateQueries([
        GET_LOGS_DATA,
        searchParams.get("nodeId"),
      ]);

      if (bottomNavbarOpen && selectedBottomNavbar === "graph") {
        queryClient.invalidateQueries([
          GET_GRAPH_DATA,
          searchParams.get("nodeId"),
        ]);
      }
    },
    onError: () => {
      showErrorNotification("Unable to add attributes!");
    },
  });

  const recursiveRemove = (list, id, parent?: any) => {
    return list
      ?.map((item) => {
        return { ...item };
      })
      ?.filter((item) => {
        if (item?.children) {
          item.children = recursiveRemove(item?.children, id, item);
          item.countChildren = item.children.length;
        }

        if (parent && item.key == id && parent.children.length === 1) {
          parent.isLeaf = true;
        }
        return item.key != id;
      });
  };

  const isMandatoryMultiplicity = (attribute) => {
    if (!ATTRIBUTES_WITH_NO_MANDATORY.includes(attribute.type)) {
      return attribute?.mandatory;
    }

    if (attribute.type === "relation") {
      const multiplicity = attribute.multiplicity?.split("..")[0];
      if (!["0", "n"].includes(multiplicity)) {
        return true;
      }
    } else if (
      attribute.type === "compound" ||
      attribute.type === "compoundSimple" ||
      attribute.type === "roleMatrix"
    ) {
      const multiplicityOfList1 = attribute.multiplicityList1?.split("..")[0];
      const multiplicityOfList2 = attribute.multiplicityList2?.split("..")[0];
      if (!["0", "n"].includes(multiplicityOfList1)) {
        return true;
      }
      if (!["0", "n"].includes(multiplicityOfList2)) {
        return true;
      }
    }
    return false;
  };

  const handleCancel = () => {
    if (searchParams.get("draft")) {
      const newData = recursiveRemove(treeData, searchParams.get("nodeId"));
      setTreeData([...newData]);

      if (newData.length > 0) {
        const selectedNode = newData[0];
        if (selectedNode) {
          navigate(`${WEB_URL}?nodeId=${selectedNode?.key}`);
          dispatch(setRefreshBreadcrumbs(true));
        }
      } else {
        navigate(WEB_URL);
        dispatch(setRefreshBreadcrumbs(true));
      }
    } else {
      queryClient.invalidateQueries([
        GET_NODE_ATTRIBUTES_DETAILS,
        searchParams.get("nodeId"),
      ]);

      const bodyResponse = queryClient.getQueryData([
        GET_NODE_ATTRIBUTES_DETAILS,
        searchParams.get("nodeId"),
      ]) as INodeDetails;

      const selectedTemplateAttributes =
        templatesData[bodyResponse?.templateId]?.attributeTemplates || [];

      const attributes = [];
      const mandatoryAttributes = [];
      selectedTemplateAttributes?.forEach((attribute: IAttributes) => {
        const attributeValue = bodyResponse?.body?.find(
          (item) => item.id == attribute.id
        );
        if (attributeValue) {
          if (attribute?.mandatory) {
            mandatoryAttributes.push(attribute.id);
          }

          attributes.push({
            ...attributeValue,
            ...attribute,
            value:
              attribute.type === "multiplicity"
                ? {
                    text1: attributeValue?.value?.split("..")[0],
                    text2: attributeValue?.value?.split("..")[1],
                  }
                : attribute.type === "switch"
                ? attributeValue?.value || false
                : attributeValue?.value,
            mandatory: isMandatoryMultiplicity(attribute),
          });
        }
      });

      setMandatoryAttributes([...mandatoryAttributes]);
      setAttributes([...attributes]);
    }

    dispatch(setDisplaySaveButton(false));
    setEditingAttribute(false);
    dispatch(setMask(false));
    setEditMode(false);
    setSpecialAttribute(false);
  };

  useEffect(() => {
    dispatch(setMask(editMode));
  }, [editMode]);

  useEffect(() => {
    if (
      !metamodel ||
      !dataSource ||
      !isUsers ||
      !roles ||
      !isActions ||
      !isAuthors
    ) {
      dispatch(setRefreshBreadcrumbs(true));
    }
  }, [params?.nodeId, isUsers]);

  // Use the useFlags hook to get flags, bitFlags, and attributeBitFlags
  const { getFlags, getAttributeBitFlags } = useFlags();

  const handlePublish = () => {
    let allAttributes = JSON.parse(JSON.stringify([...attributes]));

    // Get attributeBitFlags using the useFlags hook
    const attributeBitFlags = getAttributeBitFlags(searchParams.get("nodeId"));

    // Get the bitFlags from the query cache
    const bitFlags = queryClient.getQueryData("GET_BITFLAGS");

    allAttributes = allAttributes.filter((attr) => {
      if (attr.bitFlag !== undefined) {
        const flags = getFlags(attr.bitFlag);
        return !flags.includes("EDIT_VALUE_OFF");
      }
      return true;
    });

    allAttributes.forEach((attr) => {
      if (attr.type === "relation") {
        attr.value = attr.value || [];
      }

      if (
        attr.type?.startsWith("compound") ||
        attr.type?.startsWith("roleMatrix")
      ) {
        attr.value = attr.value ? attr.value : [];
      }

      delete attr.mandatory;
      delete attr.help;
      delete attr.order;
      delete attr.items;
      delete attr.regex;
      delete attr.dictionary;
      delete attr.filterMenu;
      delete attr.filterTemplate;
      delete attr.multiplicityList1;
      delete attr.multiplicityList2;
      delete attr.nameList1;
      delete attr.nameList2;
    });

    if (searchParams.get("draft")) {
      const selectedNode = searchRecursivelyByID(
        treeData,
        searchParams.get("nodeId")
      );

      if (selectedNode) {
        if (selectedNode.templateId == TEMP_GROUPING_NODE_ID) {
          allAttributes.push({
            id: "-1",
            name: "Allowed Children",
            type: "allowedChildren",
            value: selectedNode.allowedChildren,
          });
        } else if (selectedNode.templateId == USER_TEMPLATE_ID) {
          allAttributes.push({
            id: -11,
            name: "#sys.settings.group#",
            type: "blackbox",
            value: {},
          });
        }
        addNodeMutation.mutate({
          name: selectedNode.name,
          id: selectedNode.parentId,
          templateId: selectedNode.templateId,
          nodeType: "DATA",
          body: allAttributes,
          bitFlags: bitFlags,
          attributeBitFlags: attributeBitFlags,
        });
      }
    } else {
      const selectedNode = searchRecursivelyByID(
        treeData,
        searchParams.get("nodeId")
      );
      if (selectedNode?.templateId == USER_TEMPLATE_ID) {
        const nodeDetails = queryClient.getQueryData([
          GET_NODE_ATTRIBUTES_DETAILS,
          searchParams.get("nodeId"),
        ]) as INodeDetails;
        const blackboxAttribute = nodeDetails?.body?.find(
          (item) => item?.type === "blackbox"
        );
        if (blackboxAttribute) allAttributes.push(blackboxAttribute);
      }

      mutation.mutateAsync({
        id: searchParams.get("nodeId"),
        body: allAttributes,
        specialAttribute: specialAttribute,
        bitFlags: bitFlags,
        attributeBitFlags: attributeBitFlags,
      });
      setSpecialAttribute(false);
    }
    setInitialAttributes(deepClone(attributes));
  };

  // const [saveButtonTooltip, setSaveButtonTooltip] = useState("");

  useEffect(() => {
    setDisabledInfo("");
    let disabled = false;
    attributes.forEach((item: any) => {
      const isEmpty =
        (item?.type === "switch" && item?.value === null) ||
        !item?.value ||
        item?.value?.length === 0 ||
        (item?.type === "dropdownItems" && (item?.value || []).length === 0);

      const invalidRegex =
        item?.regex && !new RegExp(item?.regex).test(item?.value);

      if ((mandatoryAttributes.includes(item.id) && isEmpty) || invalidRegex) {
        disabled = true;
      }

      if (
        item?.type === "compound" ||
        item?.type === "compoundSimple" ||
        item?.type === "roleMatrix"
      ) {
        const multiplicityList1 = item?.multiplicityList1?.split("..");
        // const multiplicityList2 = item?.multiplicityList2?.split("..");
        const valueLength = item?.value ? item?.value?.length : 0;

        if (multiplicityList1?.length > 0) {
          const min = multiplicityList1[0];
          const max = multiplicityList1[1];

          if (min === "n" && max === "n") {
            //
          } else if (max === "n") {
            if (!(valueLength >= min)) {
              disabled = true;
            }
          } else if (!(valueLength >= min && valueLength <= max)) {
            disabled = true;
          }
        }

        // let list2Count = 0;
        // item?.value?.forEach((parent) => {
        //   if (parent?.value) {
        //     list2Count += parent?.value?.length;
        //   }
        // });

        // const min = multiplicityList2[0];
        // const max = multiplicityList2[1];

        // if (min === "n" && max === "n") {
        //   //
        // } else if (max === "n") {
        //   if (!(list2Count >= Number(min))) {
        //     disabled = true;
        //   }
        // } else if (!(list2Count >= min && list2Count <= max)) {
        //   disabled = true;
        // }
      }

      if (item?.type === "relation" || item?.type === "multipleSelect") {
        const value =
          item?.type === "relation"
            ? item?.value || []
            : Object.keys(item?.value || {});

        const multiplicity = item?.multiplicity?.split("..");

        if (multiplicity?.length > 0) {
          const min = multiplicity[0];
          const max = multiplicity[1];

          if (min === "n" && max === "n") {
            //
          } else if (max === "n") {
            if (!(value?.length >= min)) disabled = true;
          } else if (!(value?.length >= min && value?.length <= max)) {
            disabled = true;
            setDisabledInfo(`Invalid multiplicity for ${item?.name}`);
          }
        }
      }

      if (item?.type === "number" && item?.value) {
        if (
          BigInt(item?.value) > MAX_SUPPORTED_POSITIVE_VALUE ||
          BigInt(item?.value) < MIN_SUPPORTED_NEGATIVE_VALUE
        ) {
          disabled = true;
        }
      }

      if (item?.type === "decimal" && item?.value) {
        if (item?.value?.toString()?.includes(".")) {
          const decimalPart = item?.value?.toString()?.split(".")[1];
          if (decimalPart?.length === 0) {
            disabled = true;
            setDisabledInfo("Invalid decimal number!");
          }
          if (decimalPart && decimalPart.length > 12) {
            disabled = true;
          }
        } else {
          disabled = true;
          setDisabledInfo("Invalid decimal number!");
        }
      }
    });
    setDisabled(disabled);
  }, [attributes, templatesData]);

  const closeEditMode = () => {
    if (selected.keys.length > 1) {
      const onlySelectedCurrentNode = selected.info.filter(
        (item) => item.id === Number(searchParams.get("nodeId"))
      );
      dispatch(setTempBreadcrumbs(null));
      dispatch(
        setSelected({
          keys: [Number(searchParams.get("nodeId"))],
          info: onlySelectedCurrentNode,
        })
      );
    }
    if (editingAttribute) setEditingAttribute(false);
  };

  useEffect(() => {
    if (selected.info.length > 0) {
      if (selected.info[0].isLeaf && isChildrensShown) {
        setIsChildrensShown(false);
      }
    }
  }, [selected.info]);

  // const handleMetamodelPublish = () => {
  //   publishMetamodelMutation.mutate();
  // };

  const bottomNavigationMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );

  const groupStyles = (isVisible: boolean) => {
    return {
      width: 25,
      height: 20,
      padding: 0,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      background: isVisible ? "#fff" : "transparent",
      color: isVisible ? theme.colorPrimary : "#fff",
    };
  };

  return (
    <Main>
      {contextHolder}
      <BreadCrumb
        setAction={setAction}
        extra={
          <div style={{ padding: "2px 0" }} className="breadcrumbs-button">
            {!displaySaveButton && !searchParams.get("draft") && (
              <>
                {!metamodel && selected.info.length > 0 && (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "5px",
                      overflow: "hidden",
                    }}
                  >
                    {!selected.info[0]?.isLeaf && (
                      <Tooltip
                        title={
                          isChildrensShown ? t("Hide List") : t("Show List")
                        }
                        placement="bottom"
                      >
                        <Button
                          type="text"
                          disabled={bottomNavigationMask}
                          icon={
                            isChildrensShown ? (
                              <MenuFoldOutlined />
                            ) : (
                              <MenuUnfoldOutlined />
                            )
                          }
                          onClick={() => setIsChildrensShown((v) => !v)}
                          style={{
                            ...groupStyles(isChildrensShown),
                          }}
                        />
                      </Tooltip>
                    )}
                    <Tooltip
                      title={hideAllPathNames ? t("Show Path") : t("Hide Path")}
                      placement="bottom"
                    >
                      <Button
                        type="text"
                        disabled={bottomNavigationMask}
                        icon={
                          !hideAllPathNames ? (
                            <NodeCollapseOutlined />
                          ) : (
                            <NodeExpandOutlined />
                          )
                        }
                        onClick={() =>
                          dispatch(setHideAllPathNames(!hideAllPathNames))
                        }
                        style={{ ...groupStyles(hideAllPathNames) }}
                      />
                    </Tooltip>
                  </div>
                )}
              </>
            )}

            {((searchParams.get("nodeId") && displaySaveButton) ||
              searchParams.get("draft")) && (
              <Button
                type="primary"
                onClick={handleCancel}
                className="cancel-button"
              >
                {t("Cancel")}
              </Button>
            )}
            {((searchParams.get("nodeId") && displaySaveButton) ||
              searchParams.get("draft")) && (
              <Tooltip title={t(disabledInfo)} placement="bottomLeft">
                <Button
                  type="primary"
                  onClick={handlePublish}
                  disabled={disabled}
                  className="save-button"
                  loading={addNodeMutation.isLoading || mutation.isLoading}
                >
                  {t("Save")}
                </Button>
              </Tooltip>
            )}
          </div>
        }
      />
      <Wrapper>
        <Container>
          <Sidebar
            action={action}
            setAction={setAction}
            closeEditMode={closeEditMode}
            dropdownOpen={dropdownOpen}
            setDropdownOpen={setDropdownOpen}
            treeData={treeData}
            setTreeData={setTreeData}
          />

          <RightDiv style={{ height: "100%" }} id="details-parent-container">
            {searchParams.get("nodeId") && (
              <>
                <Content
                  id="details-content-container"
                  onClick={closeEditMode}
                  style={
                    searchParams.get("draft") || displaySaveButton
                      ? { border: "1px solid red", height: "100%" }
                      : {}
                  }
                >
                  {bottomNavigationMask && <div className="mask" />}
                  <WorkspaceContainer
                    attributes={attributes}
                    initialAttributes={initialAttributes}
                    setInitialAttributes={setInitialAttributes}
                    treeData={treeData}
                    setAttributes={setAttributes}
                    editingAttribute={editingAttribute}
                    setEditingAttribute={setEditingAttribute}
                    setMandatoryAttributes={setMandatoryAttributes}
                    mandatoryAttributes={mandatoryAttributes}
                    setSpecialAttribute={setSpecialAttribute}
                    updateAllowedChildrens={(templateIds: number[]) => {
                      const newTreeData = [...treeData];
                      updateAllowedChildrens(
                        newTreeData,
                        templateIds,
                        setAction,
                        setDropdownOpen
                      );

                      setTreeData(newTreeData);
                    }}
                  />
                  {isChildrensShown && (
                    <ChildrenTableView
                      onClose={() => setIsChildrensShown(false)}
                    />
                  )}
                </Content>
                <BottomNavigationDrawer
                  metamodel={metamodel}
                  displaySaveCancel={bottomDrawerMask}
                />
              </>
            )}
          </RightDiv>
        </Container>
      </Wrapper>
    </Main>
  );
};

export const DetailsPage = withErrorBoundary(DetailsPageBase, "error.generic");

const Main = styled.div`
  overflow: hidden;
  height: 100%;
  width: 100%;
  min-width: 150px;

  & .ant-segmented-item-label {
    min-height: 22px;
    line-height: 22px;
  }
  & .breadcrumbs-button {
    display: flex;
    gap: 10px;
    & button {
      font-size: 13px;
      height: 24px;
      padding: 0px 10px;
      border-radius: 3px;
      &:disabled {
        border: none;
        color: #fff;
        opacity: 0.7;
        background: gray;
      }
    }
  }
`;
const Content = styled.div`
  flex: 1;
  position: relative;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  & .mask {
    height: 100%;
    width: 100%;
  }
`;

const RightDiv = styled.div`
  flex: 2;
  display: flex;
  z-index: 1;
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
  flex-direction: column;
  background: #fff;

  & .ant-breadcrumb > ol {
    & > li:last-child {
      color: ${theme.primary};
      font-weight: 500;
    }
  }

  & .ant-menu-overflow {
    line-height: 38px !important;
  }
`;
const Container = styled.div`
  display: flex;
  flex: 1;
  height: 100%;

  & .p-splitter {
    width: 100%;
    height: 100%;
    overflow: auto;
  }

  & .p-splitter-panel {
    overflow-x: auto;
  }
`;

const Wrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f3f2ed;
  height: calc(100% - 32px);
`;
