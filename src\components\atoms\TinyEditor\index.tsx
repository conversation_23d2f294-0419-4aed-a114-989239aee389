import { styled } from "@linaria/react";
import { Button, Input } from "antd";
import i18next from "i18next";
import { Dialog } from "primereact/dialog";
import { useCallback, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { LexicalEditor } from "./LexicalEditor";

interface Props {
  value: any;
  onEditorChange?: any;
  minimal?: boolean;
  noFocus?: boolean;
  disabled?: boolean;
}

type IframeType = {
  url: string;
  height: string;
  width: string;
};

const TinyEditor = ({
  value,
  onEditorChange,
  minimal,
  noFocus,
  disabled,
}: Props) => {
  const editorRef = useRef<any>(null);
  const { t } = useTranslation();

  const [iframe, setIframe] = useState<IframeType | null>(null);
  const [showIframeModal, setShowIframeModal] = useState(false);
  const editorProperties = useSelector(
    (state: RootState) => state.globalSettings.editorProperties
  );

  const generateURL = () => {
    if (!iframe?.url.startsWith("http")) {
      return `https://${iframe?.url}`;
    }
    return iframe?.url;
  };

  const insertIframe = () => {
    if (editorRef.current && iframe) {
      const iframeHTML = `<iframe src="${generateURL()}" width="${iframe.width}" height="${iframe.height}" frameborder="0" allowfullscreen></iframe>`;
      editorRef.current.insertHTML(iframeHTML);
      setShowIframeModal(false);
      setIframe(null);
    }
  };

  const handleEditorChange = useCallback((html: string) => {
    if (onEditorChange) {
      onEditorChange(html);
    }
  }, [onEditorChange]);

  return (
    <>
      <LexicalEditor
        ref={editorRef}
        value={value}
        onChange={handleEditorChange}
        minimal={minimal}
        noFocus={noFocus}
        disabled={disabled}
        onInsertIframe={() => setShowIframeModal(true)}
        editorProperties={editorProperties}
        language={i18next.language}
      />

      <Dialog
        visible={showIframeModal}
        onHide={() => {
          setShowIframeModal(false);
          setIframe(null);
        }}
        footer={null}
        className="export-modal draggable-modal"
        header={"Iframe"}
      >
        <Iframe>
          <div className="row">
            <label>URL</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, url: e.target.value } as IframeType);
              }}
              value={iframe?.url || ""}
            />
          </div>

          <div className="row">
            <label>{t("Width")}</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, width: e.target.value } as IframeType);
              }}
              value={iframe?.width || ""}
            />
          </div>

          <div className="row">
            <label>{t("Height")}</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, height: e.target.value } as IframeType);
              }}
              value={iframe?.height || ""}
            />
          </div>
          <div className="buttons">
            <Button
              type="primary"
              disabled={!iframe?.url}
              onClick={insertIframe}
            >
              {t("Save")}
            </Button>
          </div>
        </Iframe>
      </Dialog>
    </>
  );
};

export { TinyEditor };

const Iframe = styled.div`
  padding-bottom: 8px;

  & .row {
    margin-bottom: 8px;
  }
  & label {
    margin-bottom: 2px;
    display: block;
  }
  & .buttons {
    display: flex;
    justify-content: flex-end;
  }
`;
