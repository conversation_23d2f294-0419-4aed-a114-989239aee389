import {
  BgColorsOutlined,
  LayoutOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { styled } from "@linaria/react";
import { Button, Divider, notification, Popconfirm, Radio } from "antd";
import { useEffect, useState } from "react";
import { BreadCrumb, CustomTheme } from "../../components";
import Home1Icon from "../../assets/images/homeTheme1.png";
import Home2Icon from "../../assets/images/homeTheme2.png";
import Home3Icon from "../../assets/images/homeTheme3.png";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { getLocalSettingsDetails, saveLocalSettings } from "../../services";
import { DEFAULT_COLORS, GET_LOCAL_SETTINGS_KEY } from "../../constants";
import { useDispatch, useSelector } from "react-redux";
import {
  setCustomTheme,
  setHomeVersion,
} from "../../store/features/localSettings";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import { useTheme } from "../../utils";
import { RootState } from "../../store";

const Settings = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const theme = useTheme();

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const [homeLayoutVersion, setHomeLayoutVersion] = useState("v2");
  const [colors, setColors] = useState(DEFAULT_COLORS);

  const { data: settingsData } = useQuery(
    GET_LOCAL_SETTINGS_KEY,
    getLocalSettingsDetails,
    {
      staleTime: Infinity,
    }
  );

  useEffect(() => {
    if (settingsData && settingsData?.body[0]?.value?.homeLayout) {
      setHomeLayoutVersion(settingsData?.body[0]?.value?.homeLayout);
    }

    if (settingsData && settingsData?.body[0]?.value?.colors) {
      setColors({
        ...settingsData?.body[0]?.value?.colors,
      });
      document.body.style.setProperty(
        "--color-text",
        settingsData?.body[0]?.value?.colors?.colorPrimary
      );
    }
  }, [settingsData]);

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      dispatch(setHomeVersion(homeLayoutVersion));
      dispatch(setCustomTheme({ ...colors }));

      notification.success({
        message: t("Success!"),
        description: t("Theme and Layout updated successfully!"),
      });
      dispatch(setMask(false));
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
    },
  });

  const handleSave = () => {
    if (settingsData) {
      mutation.mutate({
        value: {
          ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
          homeLayout: homeLayoutVersion,
          colors: colors,
        },
      });
    }
  };

  const handleResetToDefault = () => {
    setColors(DEFAULT_COLORS);
    detectChange();
  };

  const detectChange = () => {
    dispatch(setMask(true));
  };

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([...breadcrumb]));
  }, []);

  const handleCancel = () => {
    dispatch(setMask(false));
    if (settingsData && settingsData?.body[0]?.value?.homeLayout) {
      setHomeLayoutVersion(settingsData?.body[0]?.value?.homeLayout);
    } else {
      setHomeLayoutVersion("v2");
    }

    if (settingsData && settingsData?.body[0]?.value?.colors) {
      setColors({
        ...settingsData?.body[0]?.value?.colors,
      });
    } else {
      setColors(DEFAULT_COLORS);
    }
  };

  return (
    <Wrapper theme={theme} style={{ border: mask ? "1px solid red" : "" }}>
      <BreadCrumb
        extra={
          mask && (
            <div className="breadcrumb-buttons">
              <Button
                type="primary"
                onClick={handleCancel}
                className="cancel-button breadcrumb-button"
              >
                {t("Cancel")}
              </Button>
              <Button
                className="breadcrumb-button"
                type="primary"
                onClick={handleSave}
                loading={mutation.isLoading}
              >
                {t("Save")}
              </Button>
            </div>
          )
        }
      />
      {!settingsData ? (
        <LoadingOutlined />
      ) : (
        <div className="content">
          <section>
            <div className="title-container">
              <h5>
                <BgColorsOutlined /> {t("Color palette")}
              </h5>
              <Popconfirm
                title={`${t("Reset all colors to default")}?`}
                description={t(
                  "This will remove all your custom theme colors!"
                )}
                onConfirm={handleResetToDefault}
                placement="bottomLeft"
                okText={t("Yes")}
                cancelText={t("No")}
              >
                <a>{t("Reset all colors to default")}</a>
              </Popconfirm>
            </div>
            <Divider />

            <CustomTheme
              colors={colors}
              setColors={(color) => {
                setColors(color);
                detectChange();
              }}
            />
          </section>
          <section>
            <h5>
              <LayoutOutlined /> {t("Change home layout")}
            </h5>
            <Divider />
            <Grid>
              <Radio.Group
                onChange={(e) => {
                  setHomeLayoutVersion(e.target.value);
                  detectChange();
                }}
                value={homeLayoutVersion}
              >
                <Radio value={"v2"}>
                  <img src={Home3Icon} />
                  <p>{t("Default")}</p>
                </Radio>
                <Radio value={"v1"}>
                  <div>
                    <img src={Home1Icon} />
                    <p>{t("Shortcuts")}</p>
                  </div>
                </Radio>
                <Radio value={"v3"}>
                  <img src={Home2Icon} />
                  <p>{t("Basic")}</p>
                </Radio>
              </Radio.Group>
            </Grid>
          </section>
        </div>
      )}
    </Wrapper>
  );
};

export default Settings;

const breadcrumb = [
  {
    title: "Settings",
    to: "/settings",
  },
  {
    title: "Theme & Layout",
    to: "/settings/theme",
  },
];

const Grid = styled.div`
  margin-bottom: 20px;
  & img {
    border: 2px solid #eee;
    width: 100%;
  }

  & > .ant-radio-group {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
  }

  & label {
    display: flex;
    align-items: center;

    & > span:last-child {
      width: 100%;
    }
  }
`;

const Wrapper = styled.div<{ theme: any }>`
  flex: 1;
  display: flex;
  flex-direction: column;

  & .breadcrumb-buttons {
    margin-left: auto;
    display: flex;
    gap: 10px;
    align-items: center;
  }
  & .content {
    overflow: auto;
  }

  & .title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    & h5 {
      margin-bottom: 0px;
    }
    & a {
      color: #1677ff;
      text-decoration: underline;
      font-size: 12px;
      cursor: pointer;
    }
  }
  & .breadcrumb-button {
    font-size: 13px;
    height: 24px;
    padding: 0px 15px;
    border-radius: 3px;
  }
  & section {
    background-color: #fff;
    padding: 16px 20px;
    margin: 20px;
    border-radius: 10px;
  }
  & h5 {
    color: ${({ theme }) => theme.colorPrimary};
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
  }
`;
