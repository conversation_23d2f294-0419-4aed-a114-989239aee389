import { ReactNode } from "react";
import { INodeFlags } from "./IFlags";
import { BitFlag } from "../constants/enums";

export interface IHistory {
  name: string;
  message: string;
  timestamp: string;
}

export interface IActions {
  id: number;
  name: string;
}

export type IAttributeValueType = "ALARM_THRESHOLD" | "WARNING_THRESHOLD";
export interface IAttributes {
  attributeTemplateId?: number;
  attributeValueType?: IAttributeValueType;
  defaultValue?: any;
  id: number;
  name: string;
  type: string;
  value: any;
  attributeId?: number;
  mandatory?: boolean | null;
  items?: number[];
  regex?: string;
  help?: string;
  bitFlag?: number;
}

export type IBreadcrumbs = {
  id?: number;
  name?: string;
  parentId?: number;
  disabled?: boolean;
  allowedChildrens?: any;
  to?: string;
  isParent?: boolean;
  title: string;
  templateId?: number;
  label?: string;
};

export interface IHyperlinks {
  id: number;
  name?: string;
  inTrash: boolean;
  path?: string;
  isAsset?: boolean;
}

export interface ISettingsVariable {
  CONNECTOR_EXTRACTOR_TEMPLATE_IDS: number[];
}

export type IBitFlags = {
  [key: number]: BitFlag;
};

export type IPermissionActions = {
  code: string;
  bitMask: number;
};

export interface IPermissions {
  global: number;
  isAdmin: boolean;
  local: {
    [key: number]: number;
  };
  profileId: number;
}

export type ITemplates = {
  id: number;
  name: string;
  bitFlag: number;
  nodeType: "SYS" | "META" | "DATA";
  icon: string;
  attributeTemplates: any[];
  allowedChildren: any;
  actions: number[];
  flag: INodeFlags[];
  testDqm: boolean;
};

export interface IRelations {
  nodeId: number;
  nodeName: string;
  nodePath: string;
  nodeTemplateId: number;
  relationName: string;
  nodeTemplateName: string;
  inTrash: boolean;
}

export type IPinned = {
  id: number;
  name: string;
  parentId: string;
  templateId: number;
  date: string;
  inTrash?: boolean;
};

export type INodes = {
  id: number;
  name: string;
  last?: boolean;
  children?: INodes[];
  body?: any[];
  countChildren: number;
  parentId: number;
  templateId?: number;
  isAsset?: boolean;
  isLeaf?: boolean;
  flag?: string[];
};

export type INodeDetails = {
  body: IAttributes[];
  templateId: number;
  permissionsId: number;
  bitFlag: number;
  parentId: string;
  visualOrder: number;
  id: string;
  name: string;
  isLeaf: boolean;
  flag: INodeFlags[];
  pathName?: string;
};

export type IHeaderMenus = {
  id: number;
  parentId: number;
  name: string;
  children: IHeaderMenus[];
  allowedChildren: {
    [key: number]: string;
  };
};

export type IDropdownOptions = {
  label?: ReactNode | string;
  key?: string;
  icon?: ReactNode;
  type?: string;
};

export type ITreeDataResponse = {
  id: number;
  name: string;
  last?: boolean;
  children?: ITreeDataResponse[];
  body?: any[];
  parentId: number;
  templateId?: number;
  isAsset?: boolean;
};

export interface IAppProperties {
  key: string;
  description: string;
  isBoolean: boolean;
  readOnly: boolean;
  value: string;
}

export interface ITreeData {
  visualOrder: number;
  id?: number;
  bitFlag: number;
  permissionsId: number;
  allowedChildrens?: any;
  breadcrumb: IBreadcrumbs[];
  children?: ITreeData[] | null;
  isLeaf: boolean;
  key: number;
  nodeType?: "META" | "DATA";
  name: string;
  countChildren: number;
  parentId: number;
  icon: ReactNode;
  body?: any;
  templateId: number;
  last?: boolean;
  title?: any;
  flag: INodeFlags[];
  pathName?: string;
}

export type IPath = {
  key: number;
  value: string;
  virtual: boolean;
};
export interface IHierarchyData {
  menuId: string;
  path: IPath[];
}

export interface ICounters {
  ATTACHMENTS: number;
  COMMENTS: number;
  HISTORY: number;
  RELATIONS: number;
  LOGS: number;
}

export interface IGraphNodes {
  id: number;
  hasChildren: boolean;
  hasRelations: boolean;
  name: string;
  inTrash?: boolean;
  parentId: number;
  isExpanded: boolean;
  isChildrensShown: boolean;
  isParentShown: boolean;
  templateId: number;
  nodeType: "link" | "child";
  hasParent: boolean;
}

export interface IGraphEdges {
  nodeFrom: number;
  nodeTo: number;
  relationName: string;
  parentId: number;
  edgeType: "RELATION" | "CHILDOF" | "PARENTOF" | "COMPOUND";
  attributeId: number;
  id: number;
  multiplicity: string;
}
export interface IAllowedChildrens {
  id: number;
  name: string;
  inTrash: boolean;
}

export interface IGraphData {
  nodes: IGraphNodes[];
  edges: IGraphEdges[];
}

export * from "./IAttributeTypes";
