import { useEffect, useState } from "react";
import { MyTable } from "../../components";
import { <PERSON><PERSON>, <PERSON>ton, Divider, <PERSON>lex, Tag } from "antd";
import { MyTooltip } from "../../components/atoms/MyTooltip";
import { GET_LOCAL_SETTINGS_KEY } from "../../constants";
import { FullscreenOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { setHomeSectionMask } from "../../store/features";
import { useMutation, useQueryClient } from "react-query";
import { ILocalSettings } from "../../interfaces";
import dayjs from "dayjs";
import { RootState } from "../../store";
import { useTranslation } from "react-i18next";
import { saveLocalSettings } from "../../services";
import { useNotification } from "../../utils/functions/customHooks";
import DummyIcon from "../../assets/images/dummy.jpeg";

const HistoryTable = ({ listeners }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const [resetTrigger, setResetTrigger] = useState(0);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [initialColumns, setInitialColumns] = useState(COLUMNS);
  const [allColumnsRequest, setAllColumnsRequest] = useState([]);
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const homeSectionMask = useSelector(
    (state: RootState) => state.mask.homeSectionMask
  );

  useEffect(() => {
    const newData = [];
    for (let i = data.length; i < 5 + data.length; i++) {
      newData.push({
        key: i,
        id: i,
        date: new Date("2022-03-01 08:26:17"),
        user: i % 2 === 0 ? "Rafał Kalinowski" : "Aiska Basnet",
        object: "Business term",
        attribute: "Status",
        oldvalue: "In progress",
        newvalue: "For acceptance",
        name: i % 2 === 0 ? "Basnet Aiska" : "Aiska Basnet",
        userImage: DummyIcon,
        version: "Current version",
        lastViewed: new Date("2022-03-01 08:26:17"),
      });
    }

    setData([...newData]);
    setLoading(false);
  }, []);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.historyTable &&
      localSettingsData?.body[0]?.value?.historyTable?.columns?.length > 0
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.historyTable?.pinned || [];
      const sort = localSettingsData?.body[0]?.value?.historyTable?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.historyTable.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({
            ...COLUMNS[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );
      setAllColumnsRequest(
        localSettingsData?.body[0]?.value?.historyTable?.columns
      );
      setPinned(localSettingsData?.body[0]?.value?.historyTable?.pinned);
      setSort(localSettingsData?.body[0]?.value?.historyTable?.sort);
      setFilters(localSettingsData?.body[0]?.value?.historyTable?.filters);
      setInitialColumns(allColumns);
    } else {
      setInitialColumns(COLUMNS);
      setAllColumnsRequest(COLUMNS?.map((col) => col.field));
    }
  }, [localSettingsData]);

  const detectChange = () => {
    dispatch(setHomeSectionMask("history"));
  };

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setHomeSectionMask(null));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setHomeSectionMask(null));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        historyTable: {
          columns: allColumnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  const handleCancel = () => {
    setResetTrigger((trigger) => trigger + 1);
    setTimeout(() => {
      dispatch(setHomeSectionMask(null));
    }, 200);
  };

  return (
    <>
      {contextHolder}
      <Flex className="header-wrapper" justify="space-between" align="center">
        <h4 {...listeners}>{t("HISTORY")}</h4>
        {!homeSectionMask && (
          <div className="actions">
            <Link to="/history" className="view-all">
              <MyTooltip title="View all">
                <FullscreenOutlined />
              </MyTooltip>
            </Link>
          </div>
        )}

        {homeSectionMask === "history" && (
          <Flex gap={10}>
            <Button
              className="breadcrumb-button cancel cancel-button"
              onClick={handleCancel}
            >
              {t("Cancel")}
            </Button>
            <Button
              className="breadcrumb-button save-button"
              onClick={handleSave}
              loading={mutation.isLoading}
            >
              {t("Save")}
            </Button>
          </Flex>
        )}
      </Flex>
      <Divider />
      <article>
        <MyTable
          loading={loading}
          height={"260px"}
          resetTrigger={resetTrigger}
          emptyMessage="No history"
          data={data}
          columns={initialColumns}
          setColumnsRequest={setAllColumnsRequest}
          detectChange={detectChange}
          excelFileName="history"
          setSort={setSort}
          setPinned={setPinned}
          setFilters={setFilters}
          initialFilters={
            localSettingsData?.body[0]?.value?.historyTable?.filters || {}
          }
        />
      </article>
    </>
  );
};

export { HistoryTable };

const COLUMNS = [
  {
    headerName: "Name",
    field: "name",
    width: 200,
  },
  {
    headerName: "Version",
    field: "version",
    width: 200,
  },
  {
    headerName: "User",
    field: "user",
    width: 200,
    cellRenderer: ({ data }) => (
      <>
        <Avatar size={32} src={data.userImage} /> {data.user}
      </>
    ),
  },
  {
    headerName: " Last Viewed",
    field: "date",
    isDate: true,
    width: 200,
    cellRenderer: ({ data }) => {
      return dayjs(data?.date).format("YYYY/MM/DD HH:mm");
    },
  },
  {
    headerName: "Attribute",
    field: "attribute",
    cellRenderer: ({ data }) => <Tag>{data.attribute}</Tag>,
    width: 200,
  },
  {
    headerName: "Old Value",
    field: "oldvalue",
    width: 200,
  },
  {
    headerName: "New Value",
    field: "newvalue",
    width: 200,
  },
];
