import { styled } from "@linaria/react";
import { PoweroffOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { Menu, Select } from "antd";
import i18next, { changeLanguage } from "i18next";
import { Logo, MyTooltip } from "../../atoms";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { ReactComponent as DownIcon } from "../../../assets/down.svg";
import { useMutation, useQueryClient } from "react-query";
import { useTheme } from "../../../utils/useTheme";
import { generateHeaderPreview } from "../../../utils/functions";
import {
  IHeaderData,
  IHeaderResponse,
  ILocalSettings,
} from "../../../interfaces";
// import { searchRecursivelyByKey } from "../../../utils/functions/recursives";
import { i18n } from "../../../utils/i18n";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { setTempBreadcrumbs } from "../../../store/features/breadcrumbs";
import { setSelected } from "../../../store/features/sidebar";
import {
  useHeaderGenerator,
  useNotification,
} from "../../../utils/functions/customHooks";
import {
  GET_HEADER_MENUS,
  GET_LOCAL_SETTINGS_KEY,
  VIEWPORT_KEY,
} from "../../../constants";
import { logout } from "../../../store/features/auth";
import HelpPdf from "../../../assets/help.pdf";
import Cookies from "universal-cookie";
import { saveLocalSettings } from "../../../services";

interface Props {
  editMode?: boolean;
  previewData?: IHeaderData[];
}

const Header = ({ editMode, previewData }: Props) => {
  const theme = useTheme();
  const params = useParams();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const { contextHolder } = useNotification();
  const navigate = useNavigate();

  const { generateHeaderData } = useHeaderGenerator();

  const [language, setLanguage] = useState(null);
  const [headerItems, setHeaderItems] = useState([]);

  const workingVersionActive = useSelector(
    (state: RootState) => state.mask.workingVersion
  );
  const cookies = new Cookies();

  const { mask, attributeMask } = useSelector(
    (state: RootState) => state.sidebar
  );
  const movingMask = useSelector((state: RootState) => state.mask.movingMask);
  const selected = useSelector((state: RootState) => state.sidebar.selected);
  const languages = useSelector(
    (state: RootState) => state.globalSettings.languages
  );
  const authenticated = useSelector(
    (state: RootState) => state.auth.authenticated
  );
  const refreshBreadcrumbs = useSelector(
    (state: RootState) => state.breadcrumbs.refreshBreadcrumbs
  );

  const settingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const mutation = useMutation(saveLocalSettings);

  useEffect(() => {
    setLanguage(i18next.language);
  }, [i18next.language]);

  // initial data fetching
  const data = queryClient.getQueryData(GET_HEADER_MENUS) as IHeaderResponse;

  useEffect(() => {
    if (!authenticated) {
      setHeaderItems([]);
      return;
    }
    if (data) {
      const allHeaderItems = generateHeaderData([...data.menu]);
      setHeaderItems(allHeaderItems);
    }
  }, [
    data,
    params?.nodeId,
    searchParams.get("nodeId"),
    refreshBreadcrumbs,
    authenticated,
  ]);

  useEffect(() => {
    // for showing preview of all header data in MenuCreater Modal
    if (editMode && previewData) {
      const allHeaderItems = generateHeaderPreview(previewData);
      setHeaderItems(allHeaderItems);
    }
  }, [previewData]);

  // change locale
  const handleLanguageChange = (value: string) => {
    setLanguage(value);
    changeLanguage(value);
    i18n.changeLanguage(value);
    if (authenticated)
      mutation.mutateAsync({
        value: {
          ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
          language: value,
        },
      });
  };

  // const logoutMutation = useMutation(logoutService, {
  //   onSuccess: () => {
  //     localStorage.removeItem("token");
  //     dispatch(setAuthenticated(false));
  //   },
  //   onError: () => {
  //     showErrorNotification("Error in logging out!");
  //   },
  // });

  // perform logout
  const handleLogout = () => {
    queryClient.clear();
    localStorage.removeItem("token");
    localStorage.removeItem(VIEWPORT_KEY);
    cookies.remove("token");
    dispatch(logout());
    navigate("/login");
  };

  const removeMultiSelected = () => {
    // remove multiple selected of sidebar items on wrapper click
    if (selected.keys.length > 1) {
      const onlySelectedCurrentNode = selected.info.filter(
        (item) => item.id === Number(searchParams.get("nodeId"))
      );
      dispatch(setTempBreadcrumbs(null));
      dispatch(
        setSelected({
          keys: [Number(searchParams.get("nodeId"))],
          info: onlySelectedCurrentNode,
        })
      );
    }
  };

  const bottomNavigationMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );

  return (
    <Wrapper theme={theme} onClick={removeMultiSelected}>
      {contextHolder}
      {(mask ||
        attributeMask ||
        bottomNavigationMask ||
        workingVersionActive ||
        movingMask) &&
        !editMode && <Mask className="mask" />}
      {!editMode && <Logo />}

      <div className="menu-wrapper">
        <Menu
          selectedKeys={[params?.nodeId]}
          triggerSubMenuAction="hover"
          rootClassName="top-header-menu"
          mode="horizontal"
          items={headerItems}
        />
      </div>

      {!editMode && (
        <RightDiv>
          {authenticated ? (
            <>
              {/* <Select
                id="language-tour-item"
                value={language}
                onChange={handleLanguageChange}
                options={languages}
                suffixIcon={<DownIcon />}
              /> */}
              <MyTooltip title="Help">
                <a
                  id="help-tour-item"
                  href={HelpPdf}
                  target={"_blank"}
                  className="help"
                >
                  <QuestionCircleOutlined />
                </a>
              </MyTooltip>
              <MyTooltip title="Logout">
                <PoweroffOutlined
                  id="logout-tour-item"
                  onClick={handleLogout}
                />
              </MyTooltip>
            </>
          ) : (
            <Select
              value={language}
              onChange={handleLanguageChange}
              options={languages?.map((item) => {
                return { ...item, title: null };
              })}
              suffixIcon={<DownIcon />}
            />
          )}
        </RightDiv>
      )}
    </Wrapper>
  );
};

export { Header };

const Mask = styled.div`
  height: 100%;
  width: 100%;
  z-index: 1;
  left: 0px;
  bottom: -1px;
`;

const RightDiv = styled.div`
  width: 20%;
  justify-content: right;

  & .help {
    height: 16px;
  }

  & .anticon-poweroff {
    margin-left: 10px;
    color: #ba1a1a !important;
  }

  & > .auto-complete {
    flex: 1;
  }

  & .search {
    max-width: 400px;
  }
`;

const Wrapper = styled.div<{ theme: any }>`
  background: #fff;
  padding: 10px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  position: relative;
  gap: 12px;

  & .menu-wrapper {
    flex: 1;
    width: 65%;

    & ul {
      width: inherit;

      & > .ant-menu-item-selected {
        background: transparent !important;
      }
    }

    & > a {
      display: flex;
      gap: 5px;
      cursor: pointer;
    }
  }
  & .ant-skeleton-button {
    min-width: 60px !important;
    margin-right: 10px;
  }

  & > svg {
    width: 18px;
    min-width: 18px;
    height: 18px;
    cursor: pointer;
    fill: ${({ theme }) => theme.colorPrimary};
    margin-top: -4px;
  }

  & .anticon {
    font-size: 16px;
    color: ${({ theme }) => theme.colorPrimary};
  }

  & > .anticon {
    font-size: 20px;
  }

  & > div:first-child {
    margin-right: 10px;
  }

  & > div {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  & a {
    color: ${({ theme }) => theme.colorPrimary} !important;
    opacity: 0.8;
    &:hover {
      opacity: 1;
    }
  }

  & .active {
    text-decoration: underline;
  }

  & .ant-select-selection-item,
  .ant-select-arrow {
    color: ${({ theme }) => theme.colorPrimary} !important;
  }
  & .ant-select-selector {
    border: 0px !important;
    box-shadow: none !important;
  }
  & .ant-select-selection-item {
    font-size: 13px;
    padding-right: 17px !important;
  }

  & .ant-select-arrow svg {
    width: 11px;
    & path {
      stroke: ${({ theme }) => theme.colorPrimary} !important;
    }
  }
  & ul {
    border-bottom: 0px;
    line-height: 30px !important;
  }
  & li {
    font-size: 13px;
    color: #094f8a;
    padding-left: 0px !important;
    padding-left: 10px !important;
  }
`;
