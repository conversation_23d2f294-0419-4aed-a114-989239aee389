import { LoadingOutlined } from "@ant-design/icons";
import { styled } from "@linaria/react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { BreadCrumb, Input } from "../../components";
import { useTheme } from "../../utils/useTheme";
import { useEffect, useRef, useState } from "react";
import ReactDragListView from "react-drag-listview";
import {
  GridContextProvider,
  GridDropZone,
  GridItem,
  move,
  swap,
} from "react-grid-dnd";
import { useNavigate } from "react-router-dom";
import {
  Button,
  ColorPicker,
  Dropdown,
  Flex,
  MenuProps,
  Modal,
  Tour,
  notification,
} from "antd";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { getLocalSettingsDetails, saveLocalSettings } from "../../services";
import { Trans, useTranslation } from "react-i18next";
import {
  DQM_GROUPING_NODES,
  GET_LOCAL_SETTINGS_KEY,
  NO_USER_ASSOCIATED,
  SETTINGS_ITEMS,
} from "../../constants";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import { RootState } from "../../store";
import { useNotification } from "../../utils/functions/customHooks";
import { useTourContent } from "../../constants/useTourContent";

type FormData = {
  name: string;
  tileColor: string;
  bgColor: string;
  fontColor: string;
};

const mockupItems = [
  "/details/dqm/" + DQM_GROUPING_NODES,
  "/business-log",
  "/technical-log",
  "/schedules",
  "/settings/profile",
  "/message",
  "/history",
];

const Settings = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const parentDivRef = useRef(null);

  const { contextHolder, showTourNotification, clearAllNotification } =
    useNotification();

  const { getSettingsTourContents } = useTourContent();

  const mask = useSelector((state: RootState) => state.sidebar.mask);
  const { role, profileId } = useSelector((state: RootState) => state.auth);

  const [tourOpen, setTourOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [width, setWidth] = useState(null);
  const [grids, setGrids] = useState({} as any);
  const [gridsStyle, setGridsStyle] = useState({} as any);
  const [tempObject, setTempObject] = useState(null);
  const [count, setCount] = useState(2);
  const [action, setAction] = useState({
    key: null,
    name: "",
    index: -1,
    isNew: false,
  });

  const getIcon = (url) => {
    return SETTINGS_ITEMS.find((item) => item.url === url)?.icon;
  };

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === parentDivRef.current) {
          setWidth(entry.contentRect.width);
        }
      }
    });

    if (parentDivRef.current) {
      resizeObserver.observe(parentDivRef.current);
    }

    return () => {
      if (parentDivRef.current) {
        resizeObserver.unobserve(parentDivRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (Object.keys(grids).length === 1) {
      setCount(2);
    }
  }, [grids]);

  function onChange(sourceId, sourceIndex, targetIndex, targetId) {
    if (!sourceId && !sourceIndex && !targetIndex && !targetId) {
      return;
    }

    if (sourceIndex === targetIndex && !targetId) {
      return;
    }
    const newGrid = { ...grids };

    setTimeout(() => {
      setIsDragging(true);
    }, 100);

    detectChange();

    if (targetId) {
      if (targetId === "TEMP") {
        const newGroupName = ` `;
        newGrid[newGroupName] = [];
        targetId = newGroupName;
        setCount(count + 1);
      }

      const index = Object.keys(newGrid).indexOf(targetId);
      const result = move(
        newGrid[sourceId],
        newGrid[targetId],
        sourceIndex,
        targetIndex
      );

      setTimeout(() => {
        setIsDragging(false);
      }, 200);

      if (index === Object.keys(newGrid).length - 1) {
        const newObject = {
          ...newGrid,
          [sourceId]: result[0],
          [targetId]: result[1],
        };

        if (newObject[sourceId].length === 0) {
          delete newObject[sourceId];
        }
        setTempObject(grids);
        if (targetId === " ") {
          setAction({ ...action, key: "add", isNew: true });
        }
        setGrids({ ...newObject });
        return;
      }
      const newObject = {
        ...newGrid,
        [sourceId]: result[0],
        [targetId]: result[1],
      };
      if (newObject[sourceId].length === 0) {
        delete newObject[sourceId];
      }
      return setGrids({ ...newObject });
    }

    if (
      sourceId !== undefined &&
      sourceIndex !== undefined &&
      targetIndex !== undefined
    ) {
      const result = swap(newGrid[sourceId], sourceIndex, targetIndex);
      setTimeout(() => {
        setIsDragging(false);
      }, 200);

      const data = {
        ...newGrid,
        [sourceId]: result,
      };
      return setGrids(data);
    }
    setTimeout(() => {
      setIsDragging(false);
    }, 200);
  }

  const validationSchema = yup.object({
    name: yup.string().required("Please enter"),
  });

  const {
    handleSubmit,
    control,
    setError,
    reset,
    formState: { errors },
    setValue,
    clearErrors,
    watch,
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      bgColor: "#f3f3f3",
      tileColor: "#ccdaec",
      fontColor: "#084375",
    },
  });

  const detectChange = () => {
    if (profileId === NO_USER_ASSOCIATED) {
      return;
    }
    if (!mask) {
      dispatch(setMask(true));
    }
  };

  const onSubmit = (data) => {
    const isNameAlreadyExists = Object.keys(grids).indexOf(data.name);

    if (
      isNameAlreadyExists !== -1 &&
      isNameAlreadyExists !== Object.keys(grids).indexOf(action.name)
    ) {
      setError("name", { message: "Group name already exists!" });
      return;
    }
    detectChange();
    if (action.isNew) {
      const newGrids = { ...grids };
      newGrids[data.name] = newGrids[" "];
      delete newGrids[" "];
      setGrids(newGrids);

      setGridsStyle({
        ...gridsStyle,
        [data.name]: {
          bgColor: data.bgColor,
          tileColor: data.tileColor,
          fontColor: data.fontColor,
        },
      });
      setAction({ key: null, name: "", index: -1, isNew: false });
      reset();
      return;
    }
    const newObj = {};
    for (const [key, value] of Object.entries(grids)) {
      if (key === action.name) {
        newObj[data.name] = value;
      } else {
        newObj[key] = value;
      }
    }
    setGrids({ ...newObj });

    const newObjStyle = {};
    for (const [key, value] of Object.entries(gridsStyle)) {
      if (key === action.name) {
        newObjStyle[data.name] = {
          bgColor: data.bgColor,
          tileColor: data.tileColor,
          fontColor: data.fontColor,
        };
      } else {
        newObjStyle[key] = value;
      }
    }
    setGridsStyle({ ...newObjStyle });
    reset();
    setAction({ key: null, name: "", index: -1, isNew: false });
  };

  useEffect(() => {
    if (!action.isNew) {
      clearErrors();
      setValue("name", action.name);
    }
  }, [action.name]);

  const getBoxPerRow = () => {
    if (width < 565) {
      return 2;
    }
    if (width < 700) {
      return 3;
    }
    if (width < 865) {
      return 4;
    }
    if (width < 1025) {
      return 5;
    }
    return 6;
  };

  const { data: localSettingsData } = useQuery(
    GET_LOCAL_SETTINGS_KEY,
    getLocalSettingsDetails,
    {
      staleTime: Infinity,
      enabled: profileId !== NO_USER_ASSOCIATED,
    }
  );

  useEffect(() => {
    if (profileId === NO_USER_ASSOCIATED) {
      const filteredSettings = SETTINGS_ITEMS.filter((item) =>
        item.allowedRoles.includes("no-person")
      );
      setGrids({
        Settings: [...filteredSettings],
      });
      return;
    }
    if (localSettingsData) {
      // Filter based on user role
      const filteredSettings = SETTINGS_ITEMS.filter((item) =>
        item.allowedRoles.includes(role)
      );

      if (
        localSettingsData?.body.length === 0 ||
        !localSettingsData?.body[0].value.settings
      ) {
        setGrids({
          Settings: [...filteredSettings],
        });
      } else {
        const savedSettings =
          localSettingsData?.body?.[0]?.value?.settings || {};
        const savedStyle = localSettingsData?.body?.[0]?.value?.settingsStyle;

        // Reconstruct saved items across all groups
        const savedUrls = new Set(
          Object.values(savedSettings)
            .flat()
            .map((item: any) => item.url)
        );

        const newItems = filteredSettings.filter(
          (item) => !savedUrls.has(item.url)
        );

        // Prepare new grids
        const newGrids = {};

        Object.keys(savedSettings).forEach((key) => {
          const filtered = savedSettings[key]?.filter((setting) =>
            filteredSettings.some((item) => item.url === setting.url)
          );
          if (filtered?.length) {
            newGrids[key] = filtered;
          }
        });

        // Add new items to a default group like "Settings"
        if (newItems.length > 0) {
          newGrids["Settings"] = [...(newGrids["Settings"] || []), ...newItems];
        }

        setGrids(
          Object.keys(newGrids).length
            ? newGrids
            : { Settings: [...filteredSettings] }
        );

        // Set grid style
        if (!savedStyle || localSettingsData?.body?.length === 0) {
          setGridsStyle({
            Settings: {
              bgColor: "#f3f3f3",
              tileColor: "#ccdaec",
              fontColor: "#084375",
            },
          });
        } else {
          setGridsStyle(savedStyle);
        }
      }

      if (
        localSettingsData?.body.length === 0 ||
        !localSettingsData?.body[0].value.settingsStyle
      ) {
        setGridsStyle({
          Settings: {
            bgColor: "#f3f3f3",
            tileColor: "#ccdaec",
            fontColor: "#084375",
          },
        });
      } else {
        setGridsStyle(localSettingsData.body[0].value.settingsStyle);
      }
    }
  }, [localSettingsData]);

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      notification.success({
        message: t("Success!"),
        description: t("Settings published successfully!"),
      });
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setMask(false));
    },
  });

  const onDragEnd = (fromIndex, toIndex) => {
    const gridKeys = Object.keys(grids);
    const item = gridKeys.splice(fromIndex, 1)[0];
    gridKeys.splice(toIndex, 0, item);
    const newGrid = {};
    gridKeys.forEach((key) => {
      newGrid[key] = grids[key];
    });
    detectChange();
    setGrids({ ...newGrid });
  };

  const handleSave = () => {
    if (localSettingsData) {
      const request = {};
      Object.keys(grids)?.forEach((item) => {
        const requestInstance = [];
        grids[item].forEach((item) => {
          requestInstance.push({
            label: item.label,
            url: item.url,
          });
        });
        request[item] = requestInstance;
      });

      mutation.mutate({
        value: {
          ...(localSettingsData?.body
            ? localSettingsData?.body[0]?.value || {}
            : {}),
          settings: request,
          settingsStyle: gridsStyle,
        },
      });
    }
  };

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs(breadcrumb));
  }, []);

  const handleCancel = () => {
    if (profileId === NO_USER_ASSOCIATED) {
      const filteredSettings = SETTINGS_ITEMS.filter((item) =>
        item.allowedRoles.includes("no-person")
      );
      setGrids({
        Settings: [...filteredSettings],
      });
      return;
    }

    if (localSettingsData) {
      const filteredSettings = SETTINGS_ITEMS.filter((item) =>
        item.allowedRoles.includes(role)
      );

      const savedSettings = localSettingsData?.body?.[0]?.value?.settings || {};
      const savedStyle = localSettingsData?.body?.[0]?.value?.settingsStyle;

      // Get all saved URLs
      const savedUrls = new Set(
        Object.values(savedSettings)
          .flat()
          .map((item: any) => item.url)
      );

      // Find new items from filteredSettings
      const newItems = filteredSettings.filter(
        (item) => !savedUrls.has(item.url)
      );

      // Filter valid saved settings and construct newGrids
      const newGrids = {};
      Object.keys(savedSettings).forEach((key) => {
        const filtered = savedSettings[key]?.filter((setting) =>
          SETTINGS_ITEMS.some((item) => item.url === setting.url)
        );
        if (filtered?.length) {
          newGrids[key] = filtered;
        }
      });

      // Append new items to "Settings" group
      if (newItems.length > 0) {
        newGrids["Settings"] = [...(newGrids["Settings"] || []), ...newItems];
      }

      setGrids(
        Object.keys(newGrids).length
          ? newGrids
          : { Settings: [...SETTINGS_ITEMS] }
      );

      // Set style
      if (!savedStyle || localSettingsData?.body?.length === 0) {
        setGridsStyle({
          Settings: {
            bgColor: "#f3f3f3",
            tileColor: "#ccdaec",
            fontColor: "#084375",
          },
        });
      } else {
        setGridsStyle(savedStyle);
      }
    }

    dispatch(setMask(false));
  };

  const getLabel = (url) => {
    return SETTINGS_ITEMS.find((item) => item.url === url)?.label;
  };

  const getURL = (url) => {
    return SETTINGS_ITEMS.find((item) => item.url === url)?.to;
  };

  useEffect(() => {
    clearAllNotification();
    if (
      !localStorage.getItem("hide-settings-tour") &&
      profileId !== NO_USER_ASSOCIATED
    ) {
      showTourNotification(
        setTourOpen,
        () => {
          localStorage.setItem("hide-settings-tour", "1");
        },
        "Know more about settings"
      );
    }
  }, []);

  return (
    <Wrapper ref={parentDivRef}>
      <Tour
        disabledInteraction
        open={tourOpen}
        onClose={() => setTourOpen(false)}
        steps={getSettingsTourContents()}
      />
      <BreadCrumb
        extra={
          mask && (
            <Flex gap={10}>
              <Button
                className="breadcrumb-button cancel-button"
                type="primary"
                onClick={handleCancel}
              >
                {t("Cancel")}
              </Button>
              <Button
                className="breadcrumb-button save-button"
                type="primary"
                onClick={handleSave}
                loading={mutation.isLoading}
              >
                {t("Save")}
              </Button>
            </Flex>
          )
        }
      />
      {contextHolder}
      <Grid style={{ border: mask ? "1px solid red" : "none", flex: 1 }}>
        <GridContextProvider onChange={onChange}>
          {!localSettingsData && profileId !== NO_USER_ASSOCIATED && (
            <div className="loader">
              <LoadingOutlined />
            </div>
          )}

          <ReactDragListView
            nodeSelector=".group"
            handleSelector=".group-name"
            onDragEnd={onDragEnd}
          >
            {Object.entries(grids).map((gridItems: any, index: number) => {
              return (
                <GroupWrapper
                  className="group"
                  key={gridItems[0]}
                  style={{
                    backgroundColor:
                      gridsStyle[gridItems[0]]?.bgColor || "#f3f3f3",
                  }}
                >
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: "edit",
                          label: t("Edit"),
                        },
                      ],
                      onClick: (e) => {
                        if (e.key === "edit") {
                          setValue(
                            "tileColor",
                            gridsStyle[gridItems[0]].tileColor
                          );
                          setValue("bgColor", gridsStyle[gridItems[0]].bgColor);
                          setValue(
                            "fontColor",
                            gridsStyle[gridItems[0]].fontColor || "#084375"
                          );
                          setAction({
                            key: "edit",
                            name: gridItems[0],
                            index: index,
                            isNew: false,
                          });
                        }
                      },
                    }}
                    trigger={["contextMenu"]}
                  >
                    <p className="group-name">{gridItems[0]}</p>
                  </Dropdown>
                  <GridDropZone
                    id={gridItems[0]}
                    boxesPerRow={getBoxPerRow()}
                    rowHeight={158}
                    style={{
                      height:
                        158 * Math.ceil(gridItems[1].length / getBoxPerRow()),
                    }}
                  >
                    {gridItems[1].map((item) => {
                      return (
                        <GridItem key={item.url} className="draggable-card">
                          <Card
                            className="draggable-card"
                            id={`item-${String(item.label).replaceAll(
                              " ",
                              ""
                            )}`}
                            theme={theme}
                            style={{
                              backgroundColor:
                                gridsStyle[gridItems[0]]?.tileColor ||
                                "#ccdaec",
                              color:
                                gridsStyle[gridItems[0]]?.fontColor ||
                                "#084375",
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              if (!mask) {
                                isDragging
                                  ? null
                                  : item?.onClick
                                  ? item.onClick()
                                  : navigate(getURL(item.url));
                              }
                            }}
                          >
                            {item.icon || getIcon(item.url)}
                            {t(getLabel(item.url))}{" "}
                            {mockupItems.includes(item.url) && (
                              <span className="mockup">{t("(mockup)")}</span>
                            )}
                          </Card>
                        </GridItem>
                      );
                    })}
                  </GridDropZone>
                </GroupWrapper>
              );
            })}
          </ReactDragListView>
          {!!localSettingsData && (
            <GroupWrapper key={"TEMP"} isNewGroup>
              <GridDropZone
                id={"TEMP"}
                className={"new-group"}
                boxesPerRow={getBoxPerRow()}
                rowHeight={158}
                style={{
                  height: 165,
                }}
              >
                <p>{t("Drag to create new group")}</p>
                <></>
              </GridDropZone>
            </GroupWrapper>
          )}
        </GridContextProvider>
      </Grid>

      {(action.key === "edit" || action.key === "add") && (
        <Modal
          centered
          open={action.key === "edit" || action.key === "add"}
          footer={null}
          onCancel={() => {
            if (action.isNew) {
              setGrids(tempObject);
            }

            setAction({ key: null, name: "", index: -1, isNew: false });
          }}
          title={action.isNew ? t("Add group") : t(`Edit`)}
        >
          <Form onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label>{t("Name")}</label>
              <Input
                error={errors.name?.message}
                name="name"
                control={control}
              />
            </div>
            <div>
              <label>{t("Background color")}</label>
              <div>
                <ColorPicker
                  showText
                  defaultValue={"#f3f3f3"}
                  value={watch("bgColor")}
                  onChange={(color) => {
                    setValue("bgColor", color.toHexString());
                  }}
                />
              </div>
            </div>

            <div>
              <label>{t("Tile color")}</label>
              <div>
                <ColorPicker
                  showText
                  value={watch("tileColor")}
                  defaultValue={"#ccdaec"}
                  onChange={(color) =>
                    setValue("tileColor", color.toHexString())
                  }
                />
              </div>
            </div>
            <div>
              <label>{t("Font color")}</label>
              <div>
                <ColorPicker
                  showText
                  value={watch("fontColor")}
                  defaultValue={"#084375"}
                  onChange={(color) =>
                    setValue("fontColor", color.toHexString())
                  }
                />
              </div>
            </div>
            <Button htmlType="submit" type="primary">
              {action.isNew ? t("Add") : t("Update")}
            </Button>
          </Form>
        </Modal>
      )}
    </Wrapper>
  );
};

export default Settings;

const breadcrumb = [
  {
    title: "Settings",
    to: "/settings",
  },
];

const Form = styled.form`
  padding-bottom: 20px;
  & > div {
    display: flex;
    border: 0.5px solid #eaeaea;
    border-radius: 4px;
    & label {
      flex: 1;
      background-color: #e3ecf861;
      border-right: 1px solid #eaeaea;
      color: #436196;
      padding: 6px 10px;
    }
    & > div {
      flex: 2;
      padding: 8px 10px;
      margin-bottom: 0px;
    }
  }
  & button {
    margin-top: 16px;
    display: flex;
    margin-left: auto;
  }
`;

const GroupWrapper = styled.div<{ isNewGroup?: boolean }>`
  position: relative;
  padding: 10px 20px;
  border-bottom: ${({ isNewGroup }) =>
    isNewGroup ? "none" : "1px solid #cecece"};
  & .group-name {
    position: absolute;
    writing-mode: vertical-lr;
    transform: scale(-1);
    color: #084375;
    font-size: 14px;
    font-weight: 600;
    left: 4px;
    top: 0px;
    height: 100%;
    cursor: pointer;
    z-index: 1;
    text-align: center;
  }

  & .new-group {
    background: #ececec;
    border: 1px dashed #b4b4b4;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #828282;
    font-size: 14px;
  }
`;
const Grid = styled.div`
  overflow-y: auto;
  overflow-x: hidden;

  & .loader {
    min-height: 58vh;
    font-size: 30px;
    display: flex;
    justify-content: center;
    position: absolute;
    inset: 0;
    z-index: 1;
    font-size: 40px;
    color: #1677ff;
  }
`;

const Card = styled.div<{ theme: any }>`
  height: 138px;
  flex-direction: column;
  padding: 5px;
  cursor: pointer;
  text-align: center;
  margin: 10px;
  gap: 5px;
  background: ${({ theme }) => theme.bgLight};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 13px;
  color: ${({ theme }) => theme.colorPrimary};
  text-decoration: none;
  & > span {
    font-size: 23px;
  }

  &:hover {
    box-shadow: 0px 0px 5px rgb(17 86 151 / 63%);
  }
`;

const Wrapper = styled.div`
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  & *::selection {
    background-color: transparent; /* Light salmon */
  }

  & .breadcrumb-button {
    font-size: 13px;
    height: 24px;
    padding: 0px 15px;
    border-radius: 3px;
  }

  & > div:first-child {
    position: sticky;
    top: 0px;
    z-index: 2;
  }
  & .react-grid-layout > div {
    background-color: bisque;
  }
`;

export const ACTION_MENUS: MenuProps["items"] = [
  {
    key: "reset",
    label: <Trans>Reset to Default</Trans>,
  },
];
