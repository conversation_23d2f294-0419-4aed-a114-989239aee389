import { ReactNode, memo, useMemo } from "react";
import { getPathname, useTheme } from "../../../utils";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { styled } from "@linaria/react";
import { Dropdown, Tooltip } from "antd";
import {
  GET_CHILDRENS,
  GET_COUNTERS,
  GET_DQM_TEST_DATA,
  GET_LOCAL_SETTINGS_KEY,
  GET_LOGS_DATA,
  GET_NODE_ATTRIBUTES_DETAILS,
  GET_SETTINGS_VARIABLE,
  getSidebarMenus,
  OBJECT_TEMPLATE_ID,
  TEMPLATES_ATTRIBUTE_TEMPLATE_ID,
} from "../../../constants";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { createWorkingVersion, saveLocalSettings } from "../../../services";
import {
  DELETED_FLAG,
  EDIT_MODE_FLAG,
  IBreadcrumbs,
  ILocalSettings,
  INodeDetails,
  INodeFlags,
  ISettingsVariable,
  NEWTEMPLATE,
} from "../../../interfaces";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  setBreadcrumb,
  setFocusedNode,
  setSelected,
} from "../../../store/features/sidebar";
import { setPinned } from "../../../store/features/pinned";
import { ProgressSpinner } from "primereact/progressspinner";
import { Dialog } from "primereact/dialog";
import { invokeAction } from "../../../services/actions";
import {
  useNotification,
  usePermissions,
} from "../../../utils/functions/customHooks";
import dayjs from "dayjs";
import {
  PushpinFilled,
  PushpinOutlined,
  WarningFilled,
} from "@ant-design/icons";
import {
  setBottomNavbarOpen,
  setSelectedBottomNavbar,
} from "../../../store/features/navigation";
import { setUpdateFlag } from "../../../store/features/workingVersion";
import { getAllNodes } from "../../../services/node";

interface Props {
  // label
  label: string;
  id: number;
  setAction: any;
  parentId: number;
  isMultiple?: boolean;
  setDropdownOpen: any;
  templateId?: number;
  icon: ReactNode;
  allowedChildren: any[];
  breadcrumbs: IBreadcrumbs[];
  isLeaf?: boolean;
  childrens?: number;
  expandNode: any;
  flag: INodeFlags[];
  count: number;
  handleSort: any;
  // Drag-and-drop visual feedback
  isDropTarget?: boolean;
  dropValidationStatus?: "valid" | "invalid" | null;
  dropPosition?: number | null;
}

/**
 * Label of Sidebar Tree
 * Right click to view options
 */
const SidebarTreeLabel = memo(
  ({
    label,
    id,
    setAction,
    parentId,
    isLeaf,
    setDropdownOpen,
    templateId,
    allowedChildren,
    icon,
    breadcrumbs,
    childrens,
    expandNode,
    flag,
    count,
    handleSort,
    isDropTarget,
    dropValidationStatus,
  }: Props) => {
    const { t } = useTranslation();
    const queryClient = useQueryClient();
    const [searchParams] = useSearchParams();
    const location = useLocation();
    const theme = useTheme();
    const params = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();

    // custom hooks
    const {
      contextHolder,
      showSuccessNotification,
      showErrorNotification,
      showWarningNotification,
    } = useNotification();
    const { getPermissions } = usePermissions();

    // redux
    const { focusedNode, selected } = useSelector(
      (state: RootState) => state.sidebar
    );
    const pinned = useSelector((state: RootState) => state.pinned.pinned);
    const { bottomNavbarOpen, selectedBottomNavbar } = useSelector(
      (state: RootState) => state.navigation
    );

    // fetching from query cache
    const bodyData = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      id.toString(),
    ]) as INodeDetails;

    const settingsData = queryClient.getQueryData(
      GET_LOCAL_SETTINGS_KEY
    ) as ILocalSettings;

    const settingsVariables = queryClient.getQueryData(
      GET_SETTINGS_VARIABLE
    ) as ISettingsVariable;

    const nodeDetails = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      searchParams.get("nodeId"),
    ]);

    const PERMISSIONS = useMemo(() => {
      return getPermissions(bodyData?.permissionsId);
    }, [bodyData?.permissionsId]);

    const metamodel = location.pathname.includes("metamodel");

    const checkAlreadyFavorite = (id) => {
      return pinned?.some((item) => item?.id == id);
    };

    const contextMenuItems = getSidebarMenus(
      templateId,
      flag,
      metamodel,
      id,
      bottomNavbarOpen && selectedBottomNavbar === "working-version",
      isLeaf,
      selected,
      bodyData?.body,
      PERMISSIONS,
      t
    );

    const checkHasAttributeTemplates = () => {
      const withAttributeTemplate = selected?.info?.find(
        (node) => node.templateId === TEMPLATES_ATTRIBUTE_TEMPLATE_ID
      );
      return !!withAttributeTemplate;
    };

    const checkHasReadOnlyTemplates = () => {
      const readOnlyNodes = selected?.info?.find((node) => node.id < 0);
      return !!readOnlyNodes;
    };

    const checkHasMixedTemplatesSelection = () => {
      const readOnlyNodes = selected?.info?.filter((node) =>
        node.flag?.includes(DELETED_FLAG)
      );
      return !(
        readOnlyNodes?.length === 0 ||
        readOnlyNodes.length === selected?.info?.length
      );
    };

    const checkHasParentSelection = () => {
      const readOnlyNodes = selected?.info?.find((node) => !node.isLeaf);
      return !!readOnlyNodes;
    };

    const findMenuItemByKey = (items: any[], key: string): any => {
      for (const item of items) {
        if (item.key === key) {
          return item;
        }
        if (item.children) {
          const found = findMenuItemByKey(item.children, key);
          if (found) return found;
        }
      }
      return null;
    };

    // on dropdown items click
    const handleSidebarMenuChange = ({ key }, id, value) => {
      // if (`add-with-template-${COMMENT_TEMPLATE_ID}` === key) {
      //   setAddComment(true);
      //   return;
      // }

      if (key?.startsWith("sort-")) {
        handleSort(key);
        return;
      }

      const selectedContextMenu = findMenuItemByKey(contextMenuItems, key);

      setDropdownOpen(true);

      if (selected.keys.length > 1) {
        if (metamodel) {
          if (checkHasAttributeTemplates()) {
            showErrorNotification(
              "Please use draft version funtionality!",
              "Operation restricted!"
            );
            return;
          }

          if (checkHasReadOnlyTemplates()) {
            showErrorNotification("Your selection has read-only objects");
            return;
          }

          if (checkHasMixedTemplatesSelection()) {
            showErrorNotification(
              "Your selection has active and disabled templates"
            );
            return;
          }
        }

        if (checkHasParentSelection() && key === "delete-all") {
          showWarningNotification(
            "Objects with children should be deactivated one at a time.",
            "Warning"
          );
          return;
        }
      }

      switch (key) {
        case "close-working-version": {
          dispatch(setBottomNavbarOpen(false));
          dispatch(setSelectedBottomNavbar(""));
          return;
        }

        case "go-to-working-version":
          dispatch(setBottomNavbarOpen(true));
          dispatch(setSelectedBottomNavbar("working-version"));
          return;

        case "create-working-version":
          createWorkingVersionMutation.mutate(id);
          return;

        default: {
          if (key.startsWith("add-with-template")) {
            expandNode(isLeaf, childrens, id);
          }
          if (key.startsWith("action")) {
            actionMutation.mutate({
              nodeId: id,
              actionId: selectedContextMenu?.actionid,
            });
          } else {
            setAction({
              id: id,
              label: value,
              key: key,
              parentId: parentId,
              title: key.startsWith("add") ? (
                <>
                  {selectedContextMenu?.label} {t("to")} {value}
                </>
              ) : (
                selectedContextMenu?.label
              ),
              templateId: selectedContextMenu?.templateid || templateId,
              allowedChildren: allowedChildren,
              isLeaf: isLeaf,
            });
          }
        }
      }
    };

    const mutation = useMutation(saveLocalSettings);

    // creating a working version
    const createWorkingVersionMutation = useMutation(createWorkingVersion, {
      onSuccess: async (data: any) => {
        queryClient.invalidateQueries([
          GET_NODE_ATTRIBUTES_DETAILS,
          searchParams.get("nodeId"),
        ]);

        // update querycache for parnet
        await getAllNodes(data?.orginal?.parentId).then(
          async (childrens: any) => {
            queryClient.setQueryData(
              [GET_CHILDRENS, data?.orginal?.parentId?.toString()],
              childrens
            );
          }
        );

        dispatch(setBottomNavbarOpen(true));
        dispatch(setSelectedBottomNavbar("working-version"));
        setTimeout(() => {
          dispatch(setUpdateFlag(true));
        }, 400);
      },
    });

    // to execute action
    const actionMutation = useMutation(invokeAction, {
      onSuccess: () => {
        if (bottomNavbarOpen && selectedBottomNavbar === "logs") {
          queryClient.invalidateQueries([
            GET_LOGS_DATA,
            searchParams.get("nodeId"),
          ]);
        }

        if (
          bottomNavbarOpen &&
          (selectedBottomNavbar === "test-execution" ||
            selectedBottomNavbar === "test-chart")
        ) {
          queryClient.invalidateQueries([
            GET_DQM_TEST_DATA,
            searchParams.get("nodeId"),
          ]);
        }
        setTimeout(() => {
          queryClient.invalidateQueries([
            GET_COUNTERS,
            searchParams.get("nodeId"),
          ]);
          queryClient.invalidateQueries([
            GET_LOGS_DATA,
            searchParams.get("nodeId"),
          ]);
        }, 1200);

        showSuccessNotification("Action executed successfully!");
      },
      onError: () => {
        showErrorNotification("Error in executing action!");
      },
    });

    const LABEL = (
      <div className={`sidebar-title `}>
        {!flag?.includes(NEWTEMPLATE) && <>{icon}</>}
        {templateId == OBJECT_TEMPLATE_ID && (
          <div
            className={
              flag?.includes(NEWTEMPLATE)
                ? "newtemplate-badge"
                : flag?.includes(EDIT_MODE_FLAG)
                ? "badge"
                : "hidden"
            }
          />
        )}{" "}
        {/* Add warning icon in tree for connector nodes */}
        {settingsVariables?.CONNECTOR_EXTRACTOR_TEMPLATE_IDS?.includes(
          templateId
        ) && (
          <div className="suspects-badge">
            {" "}
            <WarningFilled />
          </div>
        )}
        <p>{label}</p>
      </div>
    );

    // Determine drop class
    let dropClass = "";
    if (isDropTarget) {
      if (dropValidationStatus === "valid") {
        dropClass = "drop-target-valid";
      } else if (dropValidationStatus === "invalid") {
        dropClass = "drop-target-invalid";
      }
    }

    return (
      <Item
        theme={theme}
        key={id.toString()}
        draggable={false}
        className={dropClass}
      >
        {contextHolder}
        {!!count && <div className="count">{count}</div>}
        {contextMenuItems?.length > 0 ? (
          <Dropdown
            onOpenChange={(open) => {
              setDropdownOpen && setDropdownOpen(open);
              if (open) {
                if (!selected.keys.includes(id)) {
                  const selectedNodeInfo = {
                    id: id,
                    parentId: parentId,
                    name: label,
                    isLeaf: isLeaf,
                  };
                  dispatch(
                    setSelected({
                      keys: [Number(id)],
                      info: [selectedNodeInfo],
                    })
                  );
                  dispatch(setBreadcrumb([...breadcrumbs]));
                  navigate(`${getPathname()}?nodeId=${id}`);
                  const focusedNodes = { ...focusedNode };
                  focusedNodes[params?.nodeId] = id;
                  dispatch(setFocusedNode(focusedNodes));
                }
              }
            }}
            menu={{
              items: contextMenuItems,
              disabled: !nodeDetails,
              onClick: (e) => handleSidebarMenuChange(e, id, label),
            }}
            trigger={["contextMenu"]}
          >
            {LABEL}
          </Dropdown>
        ) : (
          LABEL
        )}

        {!metamodel &&
          (checkAlreadyFavorite(id) ? (
            <Tooltip title={t("Remove from favorite")} placement="topLeft">
              <PushpinFilled
                className="actions"
                onClick={(e) => {
                  e.stopPropagation();
                  const newFavorites = pinned?.filter((fav) => fav?.id !== id);

                  mutation.mutateAsync({
                    value: {
                      ...(settingsData?.body
                        ? settingsData?.body[0]?.value || {}
                        : {}),
                      pinned: newFavorites,
                    },
                  });
                  dispatch(setPinned([...newFavorites]));
                  showSuccessNotification(
                    "Selected items removed from pinned successfully!"
                  );
                }}
              />
            </Tooltip>
          ) : (
            <Tooltip title={t("Pin to favorite")} placement="topLeft">
              <PushpinOutlined
                className="actions"
                onClick={(e) => {
                  e.stopPropagation();
                  const allPinned = [
                    {
                      id: id,
                      name: label,
                      parentId: params?.nodeId,
                      templateId: templateId,
                      date: dayjs(new Date()).format("YYYY-MM-DD HH:mm"),
                    },
                    ...pinned,
                  ];
                  mutation.mutateAsync({
                    value: {
                      ...(settingsData?.body
                        ? settingsData?.body[0]?.value || {}
                        : {}),
                      pinned: [...allPinned],
                    },
                  });
                  dispatch(setPinned([...allPinned]));
                  showSuccessNotification(
                    "Selected Items Added to Pinned Successfully!"
                  );
                }}
              />
            </Tooltip>
          ))}

        {/* Popup to show when executing action */}
        <Dialog
          visible={actionMutation.isLoading}
          onHide={() => null}
          closable={false}
        >
          <ProgressSpinner style={{ width: "50px", height: "50px" }} />
        </Dialog>

        {/* {addComment && (
          <AddComment
            visible={addComment}
            onHide={() => setAddComment(false)}
            onSuccess={() => {
              showSuccessNotification("Comment added successfully!");
            }}
          />
        )} */}
      </Item>
    );
  }
);

export { SidebarTreeLabel };

const Item = styled.div<{ theme: any }>`
  position: relative;
  display: flex;
  color: ${({ theme }) => theme.colorPrimary};
  font-size: 13px;
  padding-right: 36px;

  &.drop-target-valid {
    background: #f6ffed;
    outline: 2px solid #52c41a;
    outline-offset: -2px;
    border-radius: 4px;
    transition: outline 0.2s, background 0.2s;
  }

  &.drop-target-invalid {
    background: #fff1f0;
    outline: 2px solid #ff4d4f;
    outline-offset: -2px;
    border-radius: 4px;
    transition: outline 0.2s, background 0.2s;
  }

  & .count {
    position: absolute;
    right: 0px;
    top: 0px;
    bottom: 0px;
    margin: auto;
    min-width: 23px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    max-height: 15px;
    background-color: ${({ theme }) => theme.bgAttributes};
    color: ${({ theme }) => theme.colorPrimary};
  }

  /* & .disabled-template {
    color: #878787;
    text-decoration: line-through;
  } */
  & .droppable {
    cursor: copy;
    width: 100%;
  }
  & .sidebar-title {
    display: flex;
    gap: 5px;
    padding-left: 3px;
    align-items: center;

    & > p {
      position: relative;
      white-space: pre;
    }

    & .newtemplate-badge {
      height: 16px;
      width: 16px;
      background: #fdf102;
      border-radius: 50%;
    }
    & .badge {
      height: 10px;
      width: 10px;
      background: #fdf102;
      left: 11px;
      position: absolute;
      z-index: 1;
      top: 0px;
      border-radius: 50%;
    }

    & .suspects-badge {
      color: red;
    }

    & img {
      width: 18px;
      height: 18px;
      object-fit: contain;
    }
  }

  & .anticon-pushpin,
  .sort {
    font-size: 14px !important;
    top: 3px;
  }
  & .anticon-pushpin {
    right: 22px !important;
  }

  & .sort {
    right: 40px !important;
  }
  & .actions {
    color: ${({ theme }) => theme.colorPrimary};
    position: absolute;
    opacity: 0;
    top: 2px;
    font-size: 18px;
    transition: all 0.3s ease-in;
    right: 0px;
    &:hover {
      opacity: 0.8;
    }
  }
  &:hover .actions {
    opacity: 1;
    transition: all 0.2s ease-in;
  }

  & .ant-dropdown-trigger {
    width: 100%;
  }
`;
