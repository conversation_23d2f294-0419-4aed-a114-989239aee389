import { useSelector } from "react-redux";
import { RootState } from "../../store";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { styled } from "@linaria/react";
import { MessageTable } from "./MessageTable";
import { CommentsTable } from "./CommentsTable";
import { PinnedTable } from "./PinnedTable";
import { HistoryTable } from "./HistoryTable";
import { useTheme } from "../../utils/useTheme";

const SortableItem = (props) => {
  const theme = useTheme();
  const homeSectionMask = useSelector(
    (state: RootState) => state.mask.homeSectionMask
  );
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({
      id: props.id,
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const getContent = (key: string) => {
    switch (key) {
      case "pinned":
        return (
          <InfoDiv
            theme={theme}
            style={
              homeSectionMask === "pinned"
                ? {
                    border: "1px solid red",
                    zIndex: "6000",
                    position: "relative",
                  }
                : {}
            }
          >
            <PinnedTable listeners={listeners} />
          </InfoDiv>
        );
      case "comments":
        return (
          <InfoDiv
            theme={theme}
            style={
              homeSectionMask === "comments"
                ? {
                    border: "1px solid red",
                    zIndex: "6000",
                    position: "relative",
                  }
                : {}
            }
          >
            <CommentsTable listeners={listeners} />
          </InfoDiv>
        );
      case "messages":
        return (
          <InfoDiv
            theme={theme}
            style={
              homeSectionMask === "messages"
                ? {
                    border: "1px solid red",
                    zIndex: "6000",
                    position: "relative",
                  }
                : {}
            }
          >
            <MessageTable listeners={listeners} />
          </InfoDiv>
        );
      case "history":
        return (
          <InfoDiv
            theme={theme}
            style={
              homeSectionMask === "history"
                ? {
                    border: "1px solid red",
                    zIndex: "6000",
                    position: "relative",
                  }
                : {}
            }
          >
            <HistoryTable listeners={listeners} />
          </InfoDiv>
        );
    }
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      {getContent(props.id)}
    </div>
  );
};

export { SortableItem };

const InfoDiv = styled.div<{ theme?: any }>`
  border: 1px solid #eee;
  padding: 0px 20px 10px 20px;
  border-radius: 10px;
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  & ::selection {
    background: transparent;
  }
  &::-moz-selection {
    background: transparent;
  }

  & .actions {
    display: flex;
    gap: 15px;
    font-size: 17px;
    color: ${({ theme }) => theme.colorPrimary};
  }

  & .view-all {
    color: ${({ theme }) => theme.colorPrimary};
    display: flex;
    align-items: center;
    justify-content: center;

    & .anticon {
      font-size: 17px;
      width: 17px;
      height: 17px;
      cursor: pointer;
    }

    &:hover {
      opacity: 0.8;
    }
  }

  & article,
  .data-table-wrapper {
    height: 100%;
  }

  & .p-datatable {
    display: flex;
    height: 100%;
    flex-direction: column;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
`;
