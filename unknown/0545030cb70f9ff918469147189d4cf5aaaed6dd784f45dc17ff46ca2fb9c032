import { PlusOutlined } from "@ant-design/icons";
import { ReactComponent as NewTabIcon } from "../../assets/newTab.svg";
import { IDropdownOptions, ITemplates } from "../../interfaces";
import { Trans } from "react-i18next";

// used in breadcrumbs to generate options for templates
export const generateMetamodelOptions = (
  templatesData: { number: ITemplates },
  id: number,
  globalPermissions: string[],
  isLast?: boolean
): IDropdownOptions[] => {
  if (!templatesData) {
    return;
  }
  const metamodelTemplate = templatesData[Number(id)];
  const templates = [] as IDropdownOptions[];

  if (metamodelTemplate && globalPermissions.includes("ADD")) {
    const sortedAllowedChildrens = [
      ...(metamodelTemplate?.allowedChildren || []),
    ].sort((a, b) => a.name.localeCompare(b.name));

    sortedAllowedChildrens?.forEach((allowedChild) => {
      templates.push({
        key: allowedChild?.id,
        label: (
          <span>
            <Trans>Add</Trans> {allowedChild?.name}
          </span>
        ),
        icon: <PlusOutlined />,
      });
    });
  }
  if (!isLast) {
    if (globalPermissions.includes("ADD")) templates.push({ type: "divider" });
    templates.push({
      label: (
        <div className="header-menus">
          <NewTabIcon className="new-tab" /> <Trans>Open in new tab</Trans>
        </div>
      ),
      key: "open",
    });
  }
  return templates;
};
