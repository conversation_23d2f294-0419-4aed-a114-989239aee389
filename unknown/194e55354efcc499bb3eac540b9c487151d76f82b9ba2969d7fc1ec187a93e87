import { Tree } from "antd";
import { ReactComponent as DownIcon } from "../../../assets/mdi_caret.svg";
import { getAllNodes } from "../../../services/node";
import { useQueryClient } from "react-query";
import type { DataNode, EventDataNode } from "antd/es/tree";
import { GET_CHILDRENS } from "../../../constants";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { ITreeData } from "../../../interfaces";
import {
  useNotification,
  useParentHeight,
  useTemplateActions,
} from "../../../utils/functions/customHooks";
import { TrashcanTreeLabel } from "./TrashcanTreeLabel";
import {
  setExpandedTrashKeys,
  setSelectedTrash,
} from "../../../store/features/trashcan";

const TrashcanTree = ({
  setAction,
  setDropdownOpen,
  dropdownOpen,
  trashData,
  setTrashData,
}) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { getTemplateIcon } = useTemplateActions();
  const { ref: containerRef, height: containerHeight } = useParentHeight();

  const { contextHolder } = useNotification();

  const { selectedTrash, expandedTrashKeys } = useSelector(
    (state: RootState) => state.trash
  );

  const updateTreeData = (
    list: DataNode[],
    key: React.Key,
    children: DataNode[]
  ): DataNode[] =>
    list.map((node) => {
      if (node.key == key) {
        return {
          ...node,
          children,
        };
      }
      if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children),
        };
      }
      return node;
    });

  const onLoadData = ({ key, children }: any) =>
    new Promise<void>(async (resolve) => {
      if (children.length > 0) {
        resolve();
        return;
      }

      try {
        // checking if already present in cache
        let data = queryClient.getQueryData([
          GET_CHILDRENS,
          key.toString(),
        ]) as ITreeData[];
        if (!data) {
          data = await getAllNodes(key);
          queryClient.setQueryData([GET_CHILDRENS, key.toString()], data);
        }

        const parentNodes = [];

        data?.forEach((value: ITreeData) => {
          parentNodes.push({
            key: value.id,
            parentId: key,
            name: value.name,
            title: value.name,
            icon: getTemplateIcon(value.templateId),
            templateId: value.templateId,
            isLeaf: value?.countChildren === 0,
            children: [],
          });
        });
        setTrashData(updateTreeData(trashData, key, parentNodes));
        dispatch(setExpandedTrashKeys([...expandedTrashKeys, Number(key)]));
        resolve();
      } catch (e) {
        resolve();
      }
    });

  const getRecursiveSelectedKeys = (
    selectedNodes,
    selectedKeys,
    selectedKeysInfo
  ) => {
    selectedNodes.forEach((item) => {
      if (!selectedKeys.includes(item.key)) {
        selectedKeys.push(item.key);
        selectedKeysInfo.push({
          id: item.key,
          parentId: item.parentId,
          name: item.name,
          isAsset: item.templateId === 2,
          templateId: item?.templateId,
          body: item?.body,
          isLeaf: item?.isLeaf,
        });
      }

      if (item.children && item.children.length > 0) {
        getRecursiveSelectedKeys(item.children, selectedKeys, selectedKeysInfo);
      }
    });
  };

  const expandNode = (isLeaf, childrens, id) => {
    if (!isLeaf && childrens < 1) {
      dispatch(setExpandedTrashKeys([...expandedTrashKeys, id]));
    }
  };

  const handleSelect = (keys: number[], event) => {
    if (!dropdownOpen) {
      if (keys.length < 2) {
        const selectedKeys = [];
        event?.selectedNodes.forEach((item) => {
          selectedKeys.push({
            id: item.key,
            parentId: item.parentId,
            name: item.name,
            isAsset: item.templateId === 2,
            templateId: item?.templateId,
            body: item?.body,
            isLeaf: item?.isLeaf,
          });
        });
        dispatch(
          setSelectedTrash({ keys: [...keys], info: [...selectedKeys] })
        );
      } else {
        const selectedKeysInfo = [];
        const selectedKeys = [];

        getRecursiveSelectedKeys(
          event?.selectedNodes,
          selectedKeys,
          selectedKeysInfo
        );

        dispatch(
          setSelectedTrash({
            keys: [...selectedKeys],
            info: [...selectedKeysInfo],
          })
        );
      }
    }
  };

  const handleExpand = (
    _,
    event: {
      node: EventDataNode<ITreeData>;
      expanded: boolean;
      nativeEvent: any;
    }
  ) => {
    const isExpandIconClicked = event.nativeEvent.target.classList.contains(
      "ant-tree-switcher-icon"
    );

    if (!isExpandIconClicked) {
      return;
    }

    let newExpandedKeys = null;
    if (expandedTrashKeys.includes(event.node.key)) {
      newExpandedKeys = expandedTrashKeys.filter(
        (key) => key != event.node.key
      );
    } else {
      newExpandedKeys = [...expandedTrashKeys, event.node.key];
    }

    dispatch(setExpandedTrashKeys([...newExpandedKeys]));
  };

  return (
    <div className="tree tree-container" ref={containerRef}>
      {contextHolder}
      <Tree.DirectoryTree
        showLine
        showIcon={false}
        multiple
        blockNode
        virtual
        height={containerHeight - 70}
        onClick={(e) => e.stopPropagation()}
        className="hide-draggable-icon"
        autoExpandParent
        onSelect={handleSelect}
        switcherIcon={(val) => {
          return (
            <DownIcon
              style={{
                transform: val.expanded ? `rotate(0deg)` : "rotate(-90deg)",
              }}
            />
          );
        }}
        titleRender={(node) => (
          <TrashcanTreeLabel
            setAction={setAction}
            id={node.key}
            label={node.name}
            parentId={node.parentId}
            setDropdownOpen={setDropdownOpen}
            templateId={node.templateId}
            allowedChildrens={node.allowedChildrens}
            icon={node.icon}
            breadcrumbs={node.breadcrumb}
            isLeaf={node.isLeaf}
            childrens={node?.children?.length || 0}
            expandNode={expandNode}
            isMeta={node.nodeType === "META"}
          />
        )}
        expandedKeys={expandedTrashKeys}
        treeData={trashData}
        selectedKeys={(selectedTrash?.keys as any) || []}
        loadData={async (treeNode) => {
          if (expandedTrashKeys.includes(treeNode.key)) {
            return onLoadData(treeNode);
          }
          return Promise.reject();
        }}
        onExpand={dropdownOpen ? () => null : handleExpand}
      />
    </div>
  );
};

export { TrashcanTree };
