import { DatePicker, Input, Select, Switch, TimePicker } from "antd";
import "dayjs/locale/pl";
import locale from "antd/es/date-picker/locale/pl_PL";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { styled } from "@linaria/react";
import React, { useEffect, useRef, useState } from "react";
import { useQueryClient } from "react-query";
import dayjs from "dayjs";
import {
  Retention,
  AddIconAttribute,
  AddDropdownItemsAttribute,
  NumberComponent,
  TinyEditor,
  EditSchedule,
} from "../../atoms";
import { DetailsContainer } from "../../organisms";
import {
  GET_ALL_TEMPLATES_KEY,
  METAATTRIBUTE_ID_COMPOUND_TEMPLATE1,
  METAATTRIBUTE_ID_COMPOUND_TEMPLATE2,
  TEMP_GROUPING_NODE_ID,
} from "../../../constants";
import { ListOfValues } from "./ListOfValues";
import { useTranslation } from "react-i18next";
import i18next from "i18next";
import { TargetTree } from "./TargetTree";
import { DELETED_FLAG, ITemplates } from "../../../interfaces";
import { EditHyperlink } from "./EditHyperlink";
import { COMPOUND_VISUALIZATION } from "../../../constants/dropdownOptions";
import { EditLifecycle } from "./EditLifecycle";
import { EditIfFrame } from "./EditIFrame";
import { FormItem } from "./styles";
import { EditAllowedChildrens } from "./EditAllowedChildrens";
import { MultiSelect } from "primereact/multiselect";
import { EditCompound } from "./EditCompound";
import { EditSQL } from "./EditSQL";
import { withErrorBoundary } from "../../withErrorBoundary";

interface Props {
  onClose: () => void;
  value: any;
  type: string;
  onEdit: any;
  dropdownItems: any[];
  attributeId: number;
  regex?: string;
  allowedChildren?: string[];
  id: string;
  attributeMultiplicity?: string;
}
const EditAttributeBase = ({
  onClose,
  value,
  type,
  onEdit,
  attributeMultiplicity,
  dropdownItems,
  regex,
  allowedChildren,
  id,
}: Props) => {
  const [val, setVal] = useState(null);
  const ref = useRef<any>();

  const [test, setTest] = useState(null);
  const [templateOptions, setTemplateOptions] = useState([]);
  const queryClient = useQueryClient();
  const [testRegex, setTestRegex] = useState(false);

  const [isDetailsOpen, setDetailsOpen] = useState(null);

  const { t } = useTranslation();

  const focusedAttributes = [
    // "editor",
    "link",
    "decimal",
    "password",
    "textarea",
    "regex",
    "text",
    "select",
    "multipleSelect",
    "timeout",
    "number",
    "String",
  ];

  const validationSchema = yup.object({
    link: yup.string(),
    url: yup.string().when("link", (link) => {
      if (link) {
        return yup.string().required("Required");
      }
    }),
  });

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    trigger,
  } = useForm<any, any>({
    resolver: yupResolver(validationSchema),
  });

  const templatesData = queryClient.getQueryData(
    GET_ALL_TEMPLATES_KEY
  ) as ITemplates[];

  useEffect(() => {
    if (type === "composite") {
      if (templatesData) {
        const templates = [];
        templatesData?.forEach((item) => {
          if (
            item.nodeType === "DATA" &&
            item.id !== TEMP_GROUPING_NODE_ID //HARD-CODED
          ) {
            templates.push({ label: item?.name, value: item?.id.toString() });
          }
        });
        setTemplateOptions([...templates]);
      }
    }
  }, []);

  useEffect(() => {
    if (type === "allowedLinks") {
      if (
        [
          METAATTRIBUTE_ID_COMPOUND_TEMPLATE1,
          METAATTRIBUTE_ID_COMPOUND_TEMPLATE2,
        ].includes(Number(id))
      ) {
        setTemplateOptions([...(dropdownItems || [])]);
      } else {
        if (templatesData) {
          const templates = [];
          templatesData?.forEach((item) => {
            if (
              item.nodeType === "DATA" &&
              item.id !== TEMP_GROUPING_NODE_ID //HARD-CODED
            ) {
              templates.push({ label: item?.name, value: item?.id.toString() });
            }
          });
          setTemplateOptions([...templates]);
        }
      }
    }
  }, []);

  useEffect(() => {
    if (type !== "password") {
      setVal(value);
    }
    // if (type === "editor" && !value) {
    //   setVal("<p><br></p>");
    //   onEdit("<p><br></p>");
    // }
    if (type === "link" || type === "iframe") {
      setValue("link", value?.label);
      setValue("url", value?.url);
    }

    if (focusedAttributes.includes(type)) {
      ref?.current.focus();
    }
  }, []);

  const getEditField = () => {
    switch (type) {
      case "String":
        return (
          <Select
            ref={ref}
            getPopupContainer={() => document.getElementById("select-wrapper")}
            defaultOpen
            autoFocus
            allowClear
            options={COMPOUND_VISUALIZATION.map((val) => {
              return { ...val, label: t(val.label), title: null };
            })}
            value={val || null}
            onChange={(_, selected: any) => {
              if (selected) {
                setVal(selected.value);
                onEdit(selected.value);
              } else {
                setVal(null);
                onEdit(null);
              }
            }}
            showSearch
            filterOption={(input, option) => {
              return (option?.label?.toLowerCase() ?? "").includes(
                input.toLowerCase()
              );
            }}
            filterSort={(optionA, optionB) =>
              (optionA?.label ?? "")
                .toLowerCase()
                .localeCompare((optionB?.label ?? "").toLowerCase())
            }
          />
        );
      case "multiplicity":
        return (
          <Multiplicity>
            <Input
              onChange={(e) => {
                if (/^[0-9]*$/.test(e.target.value)) {
                  setVal({ ...val, text1: e.target.value });
                  onEdit({ ...val, text1: e.target.value });
                }
              }}
              value={val?.text1 || ""}
            />
            <div>..</div>
            <Input
              onChange={(e) => {
                if (
                  e.target.value === "" ||
                  /^(n|[1-9][0-9]*)$/.test(e.target.value)
                ) {
                  setVal({ ...val, text2: e.target.value });
                  onEdit({ ...val, text2: e.target.value });
                }
              }}
              value={val?.text2 || ""}
            />
          </Multiplicity>
        );
      case "relation":
        return (
          <EditHyperlink
            dropdownItems={dropdownItems}
            onEdit={onEdit}
            setVal={setVal}
            val={val}
            id={id}
          />
        );
      case "lifecycle":
        return (
          <EditLifecycle
            dropdownItems={dropdownItems}
            onEdit={onEdit}
            setVal={setVal}
            val={val}
          />
        );

      case "icon":
        return <AddIconAttribute val={val} setVal={setVal} onEdit={onEdit} />;

      case "dropdownItems":
        return (
          <AddDropdownItemsAttribute
            val={val}
            setVal={setVal}
            onEdit={onEdit}
          />
        );

      // case "datePl":
      //   return (
      //     <section>
      //       <DatePicker
      //         showTime
      //         locale={locale}
      //         format="YYYY-MM-DD HH:mm"
      //         value={val ? dayjs(val, "YYYY-MM-DDTHH:mm") : null}
      //         onSelect={(value) => {
      //           const selected = dayjs(value).format("YYYY-MM-DDTHH:mm");
      //           setVal(selected);
      //           onEdit(selected);
      //         }}
      //       />
      //     </section>
      //   );

      case "dateTime":
        return (
          <section>
            <DatePicker
              showTime
              autoFocus
              locale={i18next.language.startsWith("pl") ? locale : null}
              defaultOpen
              allowClear
              format="YYYY-MM-DD HH:mm"
              value={val ? dayjs(val, "YYYY-MM-DDTHH:mm") : null}
              onChange={(value) => {
                if (value) {
                  const selected = dayjs(value).format("YYYY-MM-DDTHH:mm");
                  setVal(selected);
                  onEdit(selected);
                } else {
                  setVal(null);
                  onEdit(null);
                }
              }}
            />
          </section>
        );

      case "date":
        return (
          <section>
            <DatePicker
              autoFocus
              locale={i18next.language.startsWith("pl") ? locale : null}
              defaultOpen
              allowClear
              format="YYYY-MM-DD"
              value={val ? dayjs(val, "YYYY-MM-DD") : null}
              onChange={(value) => {
                if (value) {
                  const selected = dayjs(value).format("YYYY-MM-DD");
                  setVal(selected);
                  onEdit(selected);
                } else {
                  setVal(null);
                  onEdit(null);
                }
              }}
            />
          </section>
        );
      case "time":
        return (
          <section>
            <TimePicker
              ref={ref}
              defaultOpen
              format="HH:mm"
              needConfirm
              value={(val ? dayjs(val, "HH:mm") : null) as any}
              onChange={(value) => {
                const selected = value ? dayjs(value).format("HH:mm") : null;
                setVal(selected);
                onEdit(selected);
              }}
            />
          </section>
        );

      case "switch":
        return (
          <section>
            <Switch
              checked={val}
              onChange={(e) => {
                setVal(e);
                onEdit(e);
              }}
            />
          </section>
        );

      case "link":
        return (
          <section>
            <FormItem>
              <label>{t("Label")}</label>
              <Input
                ref={ref}
                onChange={(e) => {
                  setVal({ ...val, label: e.target.value });
                  onEdit({ ...val, label: e.target.value });
                  setValue("link", e.target.value);
                  trigger("url");
                }}
                value={val?.label || ""}
              />
            </FormItem>

            <FormItem>
              <label>URL</label>
              <Input
                onChange={(e) => {
                  setValue("url", e.target.value);
                  setVal({ ...val, url: e.target.value });
                  onEdit({ ...val, url: e.target.value });
                  trigger("url");
                }}
                value={val?.url || ""}
              />

              {errors?.url?.message && (
                <p className="error">{errors?.url.message as any}</p>
              )}
            </FormItem>
          </section>
        );

      case "iframe":
        return (
          <EditIfFrame
            values={val}
            errors={errors}
            onFieldValueChange={(name: string, value: string) => {
              setValue(name, value);
              setVal({ ...val, [name]: value });
              onEdit({ ...val, [name]: value });
              trigger(name);
            }}
          />
        );

      case "select": {
        return (
          <section id="select-wrapper">
            <Select
              ref={ref}
              getPopupContainer={() =>
                document.getElementById("select-wrapper")
              }
              defaultOpen
              autoFocus
              allowClear
              options={dropdownItems?.map((item) => {
                return { ...item, label: t(item.label), title: null };
              })}
              value={
                val &&
                dropdownItems?.some(
                  (item) => item.value === Object.keys(val)[0]
                )
                  ? Object.keys(val)[0]
                  : null
              }
              onChange={(_, b: any) => {
                if (b) {
                  setVal({ [b.value]: b.label });
                  onEdit({ [b.value]: b.label });
                } else {
                  setVal(null);
                  onEdit(null);
                }
              }}
              showSearch
              filterOption={(input, option) => {
                return (option?.label?.toLowerCase() ?? "").includes(
                  input.toLowerCase()
                );
              }}
              filterSort={(optionA, optionB) =>
                (optionA?.label ?? "")
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? "").toLowerCase())
              }
            />
          </section>
        );
      }

      case "compound":
      case "compoundSimple":
      case "roleMatrix":
        return (
          <EditCompound id={id} setVal={setVal} onEdit={onEdit} val={val} />
        );

      // remove
      case "composite":
        return (
          <ListOfValues
            val={val}
            allowedChildren={allowedChildren}
            setVal={setVal}
            onEdit={onEdit}
          />
        );

      case "allowedLinksValues":
        return (
          <section id="multiselect-wrapper">
            <MultiSelect
              ref={ref}
              options={templateOptions?.sort((a, b) =>
                a.label.localeCompare(b.label)
              )}
              value={(val?.map((item) => item.id) || []).map(String)}
              filter
              overlayVisible
              panelClassName="allowed-children-select"
              appendTo={document.getElementById("multiselect-wrapper")}
              onChange={(e) => {
                const values = [];
                e.value?.forEach((val) => {
                  const selectedTemplate = templatesData?.find(
                    (temp) => temp.id == val
                  );
                  values.push({
                    id: val,
                    name: selectedTemplate?.name,
                    inTrash: selectedTemplate?.flag?.includes(DELETED_FLAG),
                  });
                });
                setVal(values);
                onEdit(values);
              }}
            />
          </section>
        );
      case "allowedChildren":
      case "allowedLinks":
        return (
          <section id="multiselect-wrapper">
            <EditAllowedChildrens
              attributeMultiplicity={attributeMultiplicity}
              value={val}
              setVal={setVal}
              onEdit={onEdit}
            />
          </section>
        );

      case "multipleSelect":
        return (
          <section id="select-wrapper">
            <Select
              mode="multiple"
              ref={ref}
              getPopupContainer={() =>
                document.getElementById("select-wrapper")
              }
              defaultOpen
              options={dropdownItems}
              value={Object.keys(val || {})}
              popupClassName="multi-select"
              onChange={(values, items) => {
                const selected = {};
                items?.forEach((item) => {
                  selected[item.value] = item.label;
                });
                setVal(selected);
                onEdit(selected);
              }}
              filterOption={(input, option) => {
                return (option?.label?.toLowerCase() ?? "").includes(
                  input.toLowerCase()
                );
              }}
              filterSort={(optionA, optionB) =>
                (optionA?.label ?? "")
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? "").toLowerCase())
              }
            />
          </section>
        );

      case "regex":
        return (
          <section>
            <FormItem>
              <Input
                ref={ref}
                onChange={(e) => {
                  setVal(e.target.value);
                  onEdit(e.target.value);
                  setTest(null);
                }}
                value={val}
              />
            </FormItem>
            {!!val && !testRegex && (
              <a onClick={() => setTestRegex(true)} className="test-regex">
                Test regex
              </a>
            )}

            {!!val && testRegex && (
              <>
                <FormItem>
                  <label>Test Field</label>
                  <Input
                    value={test}
                    status={
                      test !== null && !new RegExp(val).test(test) && "error"
                    }
                    onChange={(e) => setTest(e.target.value)}
                  />
                  {test !== null &&
                    (new RegExp(val).test(test) ? (
                      <p className="success">Regex Matched!</p>
                    ) : (
                      <p className="error">Regex Not Matched</p>
                    ))}
                </FormItem>
              </>
            )}
          </section>
        );
      case "text":
        return (
          <section>
            <FormItem style={{ marginBottom: 10, marginLeft: 2 }}>
              <Input.TextArea
                rows={1}
                ref={ref}
                onChange={(e) => {
                  setVal(e.target.value);
                  onEdit(e.target.value?.trim());
                }}
                value={val}
                maxLength={180}
                showCount
                status={
                  !!regex && !!val && !new RegExp(regex).test(val) && "error"
                }
                autoSize
                className="textarea-normal"
              />
              {!!regex && !!val && !new RegExp(regex).test(val) && (
                <p className="error">Invalid format!</p>
              )}
            </FormItem>
          </section>
        );

      case "target":
        return (
          <section>
            <FormItem>
              <TargetTree
                val={val}
                onChange={(value) => {
                  setVal(value);
                  onEdit(value);
                }}
              />
            </FormItem>
          </section>
        );

      case "number":
        return (
          <section>
            <FormItem>
              <NumberComponent
                ref={ref}
                onChange={(value) => {
                  setVal(value);
                  onEdit(value);
                }}
                status={
                  !!regex && !!val && !new RegExp(regex).test(val) && "error"
                }
                value={val}
              />
              {!!regex && !!val && !new RegExp(regex).test(val) && (
                <p className="error">Invalid format!</p>
              )}
            </FormItem>
          </section>
        );

      case "decimal":
        return (
          <section>
            <FormItem>
              <NumberComponent
                status={
                  !!regex && !!val && !new RegExp(regex).test(val) && "error"
                }
                ref={ref}
                onChange={(value) => {
                  setVal(value);
                  onEdit(value);
                }}
                value={val}
                decimal
              />
              {!!regex && !!val && !new RegExp(regex).test(val) && (
                <p className="error">Invalid format!</p>
              )}
            </FormItem>
          </section>
        );

      case "password":
        return (
          <section>
            <FormItem>
              <Input.Password
                name="password"
                status={
                  !!regex && !!val && !new RegExp(regex).test(val) && "error"
                }
                onChange={(e) => {
                  setVal(e.target.value);
                  onEdit(e.target.value);
                }}
                value={val}
                ref={ref}
                autoComplete="new-password"
              />
              {!!regex && !!val && !new RegExp(regex).test(val) && (
                <p className="error">Invalid format!</p>
              )}
            </FormItem>
          </section>
        );

      case "sql":
        return (
          <section>
            <FormItem>
              <EditSQL
                val={val}
                onValueChange={(code) => {
                  setVal(code);
                  onEdit(code);
                }}
              />
              {!!regex && !!val && !new RegExp(regex).test(val) && (
                <p className="error">Invalid format!</p>
              )}
            </FormItem>
          </section>
        );

      case "textarea":
        return (
          <section>
            <FormItem>
              <Input.TextArea
                ref={ref}
                status={
                  !!regex && !!val && !new RegExp(regex).test(val) && "error"
                }
                onChange={(e) => {
                  setVal(e.target.value);
                  onEdit(e.target.value);
                }}
                value={val}
                autoSize
              />
              {!!regex && !!val && !new RegExp(regex).test(val) && (
                <p className="error">Invalid format!</p>
              )}
            </FormItem>
          </section>
        );

      case "scheduler":
        return <EditSchedule value={val} onEdit={onEdit} />;

      case "timeInterval":
        return (
          <section>
            <FormItem>
              <Retention
                timeinterval
                value={val}
                setValue={setVal}
                onEdit={onEdit}
              />
            </FormItem>
          </section>
        );

      case "retention":
        return (
          <section>
            <FormItem>
              <Retention
                timeinterval={false}
                value={val}
                setValue={setVal}
                onEdit={onEdit}
              />
            </FormItem>
          </section>
        );

      case "timeout":
        return (
          <section>
            <FormItem>
              <NumberComponent
                value={val}
                ref={ref}
                onChange={(value) => {
                  setVal(value);
                  onEdit(value);
                }}
                addonAfter={<p>seconds</p>}
              />
            </FormItem>
          </section>
        );

      case "editor":
        return (
          <section>
            <FormItem>
              <TinyEditor
                value={val}
                onEditorChange={(value) => {
                  setVal(value);
                  onEdit(value);
                }}
              />
              {!!regex && !!val && !new RegExp(regex).test(val) && (
                <p className="error">Invalid format!</p>
              )}
            </FormItem>
          </section>
        );
    }

    return <p>No components created for this type yet!</p>;
  };
  const handleSave = () => {
    onEdit(val);
    onClose();
  };

  return (
    <>
      <Container onSubmit={handleSubmit(handleSave)}>
        {getEditField()}
      </Container>
      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </>
  );
};

export const EditAttribute = withErrorBoundary(
  React.memo(EditAttributeBase),
  "error.generic"
);

const Multiplicity = styled.div`
  display: flex;
  gap: 4px;

  & > div {
    min-width: 30px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    background: white;
    font-size: 22px;
    color: var(--color-text);
    line-height: 18px;
  }

  & > input {
    width: 60px;
  }
`;

const Container = styled.form`
  & .p-multiselect {
    width: 100%;
  }

  & #multiselect-wrapper .p-multiselect {
    display: none;
  }
  & .full-width-datepicker {
    width: 100%;
  }
  & .test-regex {
    text-decoration: underline;
    font-size: 12px;
    cursor: pointer;
  }
  & .ant-divider {
    margin: 14px 0;
  }

  & .textarea-normal {
    resize: none;
    &:after {
      bottom: -4px;
    }
  }

  & .p-calendar {
    width: 100%;

    & input {
      padding: 6px 10px;
    }
  }

  & .active-links {
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  & .ant-radio-wrapper {
    align-items: center;
    & img {
      width: 25px;
      height: 25px;
      object-fit: contain;
      display: flex;
    }
  }
  & .uploaded-image {
    object-fit: contain;
    height: 100%;
  }

  & .ant-divider-inner-text {
    color: grey;
    font-size: 15px;
    font-weight: 400;
  }

  & .ant-input-number-group-wrapper {
    width: 100%;
  }

  /* & .actions svg {
    cursor: move;
  } */

  & .ant-input-number-group-addon p {
    color: #888888;
    font-size: 13px;
  }
  & .ant-upload {
    width: 100% !important;
  }
  & .smart-action-button {
    padding: 10px;
  }

  & .smart-flex-wrap {
    gap: 5px;
  }

  & smart-multi-combo-input {
    width: 100%;
  }
  & .buttons {
    display: flex;
    margin-top: 20px;
    gap: 16px;

    & .cancel {
      border-color: #f43939;
      color: #f43939;
    }

    & button {
      min-height: 34px;
      min-width: 80px;
      font-size: 13px;
      box-shadow: none;
      margin-bottom: 10px;
    }
  }

  & .quill {
    width: 100%;
    max-width: unset;
  }

  & .ant-picker {
    width: 100%;
    height: 36px;
  }
  & .ant-select-selector {
    min-height: 36px;
  }

  & .ant-picker-calendar-header .ant-select-selector {
    min-height: unset;
  }

  & .ant-select,
  .ant-input-number {
    width: 100%;
  }

  & .ql-editor {
    width: 100%;
    resize: vertical;
  }
  & .ant-radio-group {
    display: grid;
    grid-template-columns: repeat(5, 1fr);

    & label {
      margin-right: 0px;
      padding: 10px;

      & .anticon {
        font-size: 20px;
        color: #084f8a;
      }
    }
  }
  & h5 {
    font-size: 14px;
    font-weight: 500;
    color: #084f8a;
    margin-bottom: 11px;
  }

  & .ant-checkbox-wrapper {
    font-size: 12px;
    margin-top: 7px;
    color: #084f8b;
  }
`;
