{"name": "lexical-playground", "version": "0.33.0", "private": true, "type": "module", "scripts": {"dev": "vite --host --port 3000", "build-dev": "vite build", "build-prod": "vite build --mode production", "build-vercel": "(cd ../../ && node ./scripts/build.js) && npm run build-dev", "preview": "vite preview"}, "dependencies": {"@excalidraw/excalidraw": "^0.18.0", "@lexical/clipboard": "0.33.0", "@lexical/code": "0.33.0", "@lexical/file": "0.33.0", "@lexical/hashtag": "0.33.0", "@lexical/link": "0.33.0", "@lexical/list": "0.33.0", "@lexical/mark": "0.33.0", "@lexical/overflow": "0.33.0", "@lexical/plain-text": "0.33.0", "@lexical/react": "0.33.0", "@lexical/rich-text": "0.33.0", "@lexical/selection": "0.33.0", "@lexical/table": "0.33.0", "@lexical/utils": "0.33.0", "katex": "^0.16.10", "lexical": "0.33.0", "lodash-es": "^4.17.21", "prettier": "^2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^3.1.4", "y-websocket": "^1.5.4", "yjs": ">=13.5.42"}, "devDependencies": {"@babel/plugin-transform-flow-strip-types": "^7.24.7", "@babel/preset-react": "^7.24.7", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@types/lodash-es": "^4.14.182", "@vitejs/plugin-react": "^4.2.1", "rollup-plugin-copy": "^3.5.0", "vite": "^5.2.11", "vite-plugin-static-copy": "^2.3.0"}, "sideEffects": false}