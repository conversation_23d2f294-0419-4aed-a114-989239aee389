import { styled } from "@linaria/react";
import { useEffect, useMemo, useState } from "react";
import { API } from "../../../utils/api";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { Button, Dropdown, Empty, Select, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { Hyperlink } from "../../atoms/AttributeItem/Hyperlink";
import { DetailsContainer, StyledDataTable } from "../../organisms";
import { getInitialTableFilters } from "../../../utils/functions/getInitialTableFilters";
import { getParentID, transformObjectPath } from "../../../utils";
import { getNodeDetails } from "../../../services/node";
import { IAttributes, INodeDetails } from "../../../interfaces";
import {
  DeleteOutlined,
  EditOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { AttributeItem } from "../../atoms";
import { useSearchParams } from "react-router-dom";
import { useQueryClient } from "react-query";
import {
  ATTACHMENT_NODE_ID,
  GET_NODE_ATTRIBUTES_DETAILS,
  getAttributeIcon,
  PERMISSIONS_NODE_ID,
} from "../../../constants";
import { usePermissions } from "../../../utils/functions/customHooks";
import { NoPermissionsModal } from "../Modals";

const EditCompound = ({ id, setVal, onEdit, val }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [searchParams] = useSearchParams();

  const { getPermissions } = usePermissions();

  const [noPermissionPopup, setNoPermissionPopup] = useState(false);

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  let templateId = null;
  if (searchParams.get("template")) {
    templateId = searchParams.get("template");
  } else {
    const nodeDetails = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      searchParams.get("nodeId"),
    ]) as INodeDetails;
    templateId = nodeDetails?.templateId;
  }

  const selectedTemplate = templatesData[templateId]?.attributeTemplates;

  const attributes = selectedTemplate?.find(
    (attr) => attr?.id === id
  ) as ICompound;

  const [filtersList1, setFiltersList1] = useState(
    getInitialTableFilters([
      { key: "name" },
      {
        key: "templateId",
      },
      {
        key: "path",
      },
    ])
  );

  const [sortList1, setSortList1] = useState({
    field: null,
    order: null,
  });

  const [filtersList2, setFiltersList2] = useState(
    getInitialTableFilters([
      { key: "name" },
      {
        key: "templateId",
      },
      {
        key: "path",
      },
    ])
  );

  const [sortList2, setSortList2] = useState({
    field: null,
    order: null,
  });
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [listData1, setListData1] = useState([]);
  const [listData2, setListData2] = useState([]);
  const [editingIndex, setEditingIndex] = useState(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const [selectedList, setSelectedlist] = useState([] as ISelected[]);
  const [temp, setTemp] = useState(null);
  const [expandedRows, setExpandedRows] = useState(null);
  const [sourceType, setSourceType] = useState(null);

  const generateIndependentListData = async () => {
    const sourceIds1 = attributes?.templates1?.map((temp) => temp.id);
    const params = { addBody: true };
    if (sourceIds1?.length > 0) {
      params["templateIds"] = sourceIds1;
    }
    if (attributes?.parent1) {
      params["parentIds"] = [attributes.parent1.toString()];
    }

    const response1 = (await API.post(`/model/node/get`, params)) as any;
    const list1 = [];
    response1?.forEach((item) => {
      list1.push({
        label: item.name,
        value: item.id,
        key: item.id,
        id: item.id,
        pathName: item?.pathName,
        templateId: item?.templateId,
        templateHasAttributes: item?.body?.length > 0,
        permissionsId: item?.permissionsId,
      });
    });

    const sourceIds2 = attributes?.templates2?.map((temp) => temp.id);
    const params2 = { addBody: true };
    if (sourceIds2?.length > 0) {
      params2["templateIds"] = sourceIds2;
    }
    if (attributes?.parent2) {
      params2["parentIds"] = [attributes.parent2.toString()];
    }

    const response2 = (await API.post(`/model/node/get`, params2)) as any;
    const list2 = [];

    response2?.forEach((item) => {
      list2.push({
        label: item.name,
        pathName: item?.pathName,
        templateHasAttributes: item?.body?.length > 0,
        templateId: item?.templateId,
        value: item.id,
        key: item.id,
        id: item.id,
        permissionsId: item?.permissionsId,
      });
    });
    setListData1(list1);
    setListData2(list2);
  };

  const generateHierarchyData = async () => {
    const sourceIds = attributes?.templates1?.map((temp) => temp.id);
    const params = { addBody: true };
    if (sourceIds?.length > 0) {
      params["templateIds"] = sourceIds;
    }
    if (attributes?.parent1) {
      params["parentIds"] = [attributes.parent1.toString()];
    }

    const response = (await API.post(`/model/node/get`, params)) as any;
    const list1 = [];
    response?.forEach((parentItem) => {
      list1.push({
        label: parentItem.name,
        value: parentItem.id,
        key: parentItem.id,
        id: parentItem.id,
        templateHasAttributes: parentItem?.body?.length > 0,
        permissionsId: parentItem?.permissionsId,
      });
    });
    setListData1(list1);
  };

  const generateList2Data = async (selectedList1) => {
    const response = (await API.post(`/model/node/get`, {
      parentIds: [selectedList1],
    })) as any;
    const list = [];
    response?.forEach((parentItem) => {
      list.push({
        label: parentItem.name,
        value: parentItem.id,
        key: parentItem.id,
        id: parentItem.id,
        parentId: parentItem?.parentId,
        templateHasAttributes: parentItem.templateHasAttributes,
        permissionsId: parentItem?.permissionsId,
      });
    });
    setListData2(list);
  };

  useEffect(() => {
    const sourceType = Object.keys(attributes?.compoundSourceType || {})[0];
    setSourceType(sourceType);

    if (sourceType === "COMPOUND_HIERARCHY") {
      generateHierarchyData();
    } else {
      generateIndependentListData();
    }
  }, [attributes]);

  useEffect(() => {
    if (initialLoad && val) {
      setSelectedlist([...val]);
      setInitialLoad(false);
    }
  }, [val, initialLoad]);

  const HYPERLINKS_ACTIONS = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("Open in new tab"),
      key: "open-in-new-tab",
    },
  ];

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
      }
    }
  };

  const COLUMNS = [
    {
      label: t("Asset Name"),
      key: "label",
      dataIndex: "label",
      render: (record) => {
        return (
          <Dropdown
            menu={{
              items: HYPERLINKS_ACTIONS,
              onClick: (e) =>
                handleNodeClick(e.key, record.value, record.label),
            }}
            trigger={["contextMenu"]}
          >
            <p>{record.label}</p>
          </Dropdown>
        );
      },
    },
    {
      label: "Object Template",
      key: "templateId",
      dataIndex: "templateId",
      width: 100,
      render: (record) => {
        const selectedTemplate = templatesData[Number(record?.templateId)];
        if (!selectedTemplate) {
          return "-";
        }
        const templateIcon = selectedTemplate?.icon || "_30_folder";

        return (
          <p className="title-container">
            {getAttributeIcon(templateIcon)}
            {selectedTemplate.name}
          </p>
        );
      },
    },
    {
      label: "Path",
      key: "pathName",
      dataIndex: "pathName",
      width: 100,
      render: (data) => (
        <p className="right-align">
          {data?.pathName
            ? transformObjectPath(data?.pathName, data?.inTrash)
            : "-"}
        </p>
      ),
    },
  ];

  const handleNodeExpand = async (id) => {
    const attributes = [];
    const nodeDetails = await getNodeDetails(id);
    const selectedTemplateAttributes =
      templatesData[nodeDetails.templateId]?.attributeTemplates || [];

    const nodeAttributes = nodeDetails?.body?.filter(
      (attr) =>
        attr.id !== PERMISSIONS_NODE_ID && attr.id !== ATTACHMENT_NODE_ID
    );
    const hasNewAttributes =
      selectedTemplateAttributes?.length > nodeAttributes?.length;

    selectedTemplateAttributes?.forEach((attribute: IAttributes) => {
      const attributeValue = nodeDetails?.body?.find(
        (item) => item.id == attribute.id
      );
      if (attributeValue) {
        attributes.push({
          ...attributeValue,
          ...attribute,
          value:
            attribute.type === "multiplicity"
              ? {
                  text1: attributeValue?.value?.split("..")[0],
                  text2: attributeValue?.value?.split("..")[1],
                }
              : attribute.type === "switch"
              ? attributeValue?.value || false
              : attributeValue?.value,
        });
      }
    });

    const updatedData = [...listData1];
    const index = updatedData.findIndex((item) => item.id === id);
    updatedData[index].hasNewAttributes = hasNewAttributes || false;
    updatedData[index].attributes = [...attributes];
    setListData1([...updatedData]);
  };
  const TITLE_CLASSNAME = "hyperlinktables-title";

  const rowExpansionTemplate = (data) => {
    if (!data?.attributes) {
      return <LoadingOutlined style={{ fontSize: 24, marginLeft: 15 }} />;
    }

    if (data?.attributes?.length === 0) {
      if (data?.hasNewAttributes) {
        return (
          <div className="new-attributes">
            <p>{t("New attributes exists")}</p>
          </div>
        );
      }
      return <p>{t("No attributes11")}</p>;
    }

    return (
      <div style={{ marginLeft: 15 }}>
        {data?.attributes?.map((attribute) => (
          <AttributeItem
            readOnly
            key={attribute.id}
            {...attribute}
            title={attribute.name}
            titleClassName={TITLE_CLASSNAME}
          />
        ))}

        {data?.hasNewAttributes && (
          <div className="new-attributes">
            <p>{t("New attributes exists")}</p>
          </div>
        )}
      </div>
    );
  };

  const isEnrichedVisualization =
    Object.keys(attributes?.visualization || {})[0] === "enriched";

  const validateMultiplicityList1 = () => {
    let disabled = false;
    const multiplicityList2 = attributes?.multiplicityList1?.split("..");
    if (multiplicityList2?.length > 0) {
      const max = multiplicityList2[1];

      if (max === "n") {
        //
      } else if (selectedList.length >= Number(max)) {
        disabled = true;
      }
    }
    return disabled;
  };

  const validateMultiplicity = () => {
    let disabled = false;
    const multiplicityList2 = attributes?.multiplicityList2?.split("..");
    const min = multiplicityList2[0];
    const max = multiplicityList2[1];

    if (min === "n" && max === "n") {
      //
    } else if (max === "n") {
      if (!(temp?.value?.length >= Number(min))) {
        disabled = true;
      }
    } else if (!(temp?.value?.length >= min && temp?.value?.length <= max)) {
      disabled = true;
    }
    return disabled;
  };

  const maxCountForList2 = useMemo(() => {
    const max = attributes?.multiplicityList2?.split("..")[1];
    if (max && max !== "n") {
      return Number(max);
    }
  }, [attributes?.multiplicityList2]);

  return (
    <Wrapper>
      <Flex>
        <div className="list">
          <h6>{attributes?.nameList1}</h6>
        </div>
        <div className="list">
          <h6>
            {attributes?.nameList2} ({attributes?.multiplicityList2})
          </h6>
        </div>
        <div className="actions">
          {selectedList.length < listData1.length &&
            (!temp || temp?.id) &&
            !temp?.isNew &&
            !validateMultiplicityList1() && (
              <Tooltip title={t("Add New")}>
                <Button
                  type="primary"
                  className="flex-1 add-new"
                  icon={<i className="pi pi-plus" />}
                  onClick={() => {
                    setTemp({
                      isNew: true,
                      id: null,
                      name: null,
                      value: [],
                      templateHasAttributes: false,
                      permissionsId: 0,
                    });

                    setEditingIndex(selectedList.length);
                  }}
                />
              </Tooltip>
            )}
        </div>
      </Flex>
      {selectedList.length === 0 && !temp ? (
        <Flex>
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={false} />
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={false} />
          <div className="actions" />
        </Flex>
      ) : (
        selectedList?.map((selected, index) => (
          <Flex
            key={`${selected.id}-${index}`}
            style={{ borderBottom: "1px solid #eee" }}
          >
            {editingIndex === index ? (
              isEnrichedVisualization ? (
                <>
                  <div style={{ flex: 1, overflow: "auto" }}>
                    <StyledDataTable
                      globalFilterFields={["name", "path"]}
                      excelFileName="compound"
                      filters={filtersList1}
                      setFilters={setFiltersList1}
                      sort={sortList1}
                      withMultiplicity
                      multiplicity={attributes?.multiplicityList1}
                      setSort={setSortList1}
                      height={"50vh"}
                      columns={COLUMNS}
                      data={listData1?.map((item) => ({
                        ...item,
                        disabled: selectedList?.some(
                          (selected) => selected.id === item.value
                        ),
                      }))}
                      alignLeft
                      expandable
                      noHeader
                      onRowExpand={(e) => {
                        const permissions = getPermissions(
                          e.data?.permissionsId
                        );
                        if (permissions.includes("VIEW")) {
                          const _expanded = expandedRows || {};
                          _expanded[`${e.data.id}`] = true;
                          setExpandedRows(_expanded);
                          handleNodeExpand(e.data.id);
                        } else {
                          setNoPermissionPopup(true);
                        }
                      }}
                      noDownload
                      onRowCollapse={(e) => {
                        const _expanded = { ...(expandedRows || {}) };
                        delete _expanded[e.data.id];
                        setExpandedRows(_expanded);
                      }}
                      expandedRows={expandedRows}
                      expandCondition={(rowData) => {
                        return rowData?.templateHasAttributes;
                      }}
                      rowExpansionTemplate={rowExpansionTemplate}
                      selected={[{ ...temp }]}
                      setSelected={(selected) => {
                        if (selected) {
                          const newSelected = { ...temp };
                          newSelected.id = selected[0]?.value || null;
                          newSelected.name = selected[0]?.label || null;
                          newSelected.pathName = selected[0]?.pathName;
                          newSelected.templateId = selected[0]?.templateId;
                          newSelected.templateHasAttributes =
                            selected[0]?.templateHasAttributes || false;
                          newSelected.permissionsId =
                            selected[0]?.permissionsId;

                          setTemp(newSelected);
                        } else {
                          setTemp(null);
                        }
                      }}
                    />
                  </div>

                  <div style={{ flex: 1, overflow: "auto" }}>
                    <StyledDataTable
                      noHeader
                      withMultiplicity
                      multiplicity={attributes?.multiplicityList2}
                      globalFilterFields={["name", "path"]}
                      excelFileName="compound"
                      filters={filtersList2}
                      setFilters={setFiltersList2}
                      sort={sortList2}
                      setSort={setSortList2}
                      height={"50vh"}
                      columns={COLUMNS}
                      data={
                        sourceType === "COMPOUND_HIERARCHY"
                          ? listData2?.filter(
                              (list) => list.parentId === temp.id
                            )
                          : listData2
                      }
                      alignLeft
                      expandable
                      onRowExpand={(e) => {
                        const permissions = getPermissions(
                          e.data?.permissionsId
                        );
                        if (permissions.includes("VIEW")) {
                          const _expanded = expandedRows || {};
                          _expanded[`${e.data.id}`] = true;
                          setExpandedRows(_expanded);
                          handleNodeExpand(e.data.id);
                        } else {
                          setNoPermissionPopup(true);
                        }
                      }}
                      noDownload
                      onRowCollapse={(e) => {
                        const _expanded = { ...(expandedRows || {}) };
                        delete _expanded[e.data.id];
                        setExpandedRows(_expanded);
                      }}
                      expandedRows={expandedRows}
                      expandCondition={(rowData) => {
                        return rowData?.templateHasAttributes;
                      }}
                      rowExpansionTemplate={rowExpansionTemplate}
                      selected={temp.value}
                      setSelected={(selected) => {
                        const newSelected = { ...temp };
                        const newChildrens = selected?.map((i) => {
                          return {
                            ...i,
                            id: i.id,
                            name: i?.label || i?.name,
                            templateHasAttributes: i.templateHasAttributes,
                            permissionsId: i?.permissionsId,
                          };
                        });
                        newSelected.value = newChildrens;
                        setTemp(newSelected);
                      }}
                    />
                  </div>
                </>
              ) : (
                <>
                  <Select
                    defaultOpen
                    autoFocus
                    allowClear
                    disabled={!!selected.id}
                    options={listData1?.map((item) => ({
                      ...item,
                      title: null,
                      disabled: selectedList?.some(
                        (selected) => selected.id === item.value
                      ),
                    }))}
                    value={temp.id}
                    onChange={(_, item: any) => {
                      if (sourceType === "COMPOUND_HIERARCHY") {
                        generateList2Data(item.value);
                      }

                      const newSelected = { ...temp };
                      newSelected.id = item.value;
                      newSelected.name = item.label;
                      newSelected.templateHasAttributes =
                        item.templateHasAttributes;
                      newSelected.pathName = item?.pathName;
                      newSelected.templateId = item?.templateId;
                      newSelected.permissionsId = item?.permissionsId;

                      setTemp(newSelected);
                    }}
                    showSearch
                    filterOption={(input, option) => {
                      return (option?.label?.toLowerCase() ?? "").includes(
                        input.toLowerCase()
                      );
                    }}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? "")
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? "").toLowerCase())
                    }
                  />

                  <Select
                    mode="multiple"
                    allowClear
                    maxCount={maxCountForList2}
                    options={listData2?.map((item) => {
                      return { ...item, title: null };
                    })}
                    value={temp?.value?.map((item) => item.id) || []}
                    onChange={(_, item: any) => {
                      const newSelected = { ...temp };
                      const newChildrens = item?.map((i) => {
                        return {
                          id: i.value,
                          name: i.label,
                          templateHasAttributes: i.templateHasAttributes,
                          pathName: i?.pathName,
                          templateId: i?.templateId,
                          permissionsId: i?.permissionsId,
                        };
                      });
                      newSelected.value = newChildrens;
                      setTemp(newSelected);
                    }}
                    showSearch
                    filterOption={(input, option) => {
                      return (option?.label?.toLowerCase() ?? "").includes(
                        input.toLowerCase()
                      );
                    }}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? "")
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? "").toLowerCase())
                    }
                  />
                </>
              )
            ) : (
              <>
                <div
                  className="hyperlink"
                  // onClick={() => {
                  //   setEditingIndex(index);
                  //   setTemp(selected);
                  //   if (sourceType === "COMPOUND_HIERARCHY") {
                  //     generateList2Data(selected?.id);
                  //   }
                  // }}
                >
                  {selected.id && <Hyperlink val={[selected]} />}
                </div>
                <div
                  className="hyperlink"
                  // onClick={() => {
                  //   setEditingIndex(index);
                  //   setTemp(selected);
                  //   if (sourceType === "COMPOUND_HIERARCHY") {
                  //     generateList2Data(selected?.id);
                  //   }
                  // }}
                >
                  {selected.value?.length > 0 ? (
                    <Hyperlink val={selected.value} />
                  ) : (
                    <p className="no-data">—</p>
                  )}
                </div>
              </>
            )}

            <div className="actions">
              {editingIndex === index ? (
                <>
                  <Tooltip title={t("Save")} placement="leftBottom">
                    <Button
                      type="primary"
                      icon={<i className="pi pi-save" />}
                      className="save-btn"
                      onClick={() => {
                        const newSelected = [...selectedList];
                        newSelected[index] = temp;
                        setSelectedlist([...newSelected]);
                        setVal(newSelected);
                        onEdit(newSelected);
                        setEditingIndex(null);
                        setTemp(null);
                      }}
                      disabled={validateMultiplicity()}
                    />
                  </Tooltip>

                  <Tooltip title={t("Cancel")} placement="leftBottom">
                    <Button
                      type="primary"
                      className="cancel-btn"
                      icon={<i className="pi pi-ban" />}
                      onClick={() => {
                        if (temp.isNew) {
                          const newSelected = [...selectedList];
                          newSelected.splice(index, 1);
                          setSelectedlist([...newSelected]);
                        }
                        setTemp(null);
                        setEditingIndex(null);
                      }}
                    />
                  </Tooltip>
                </>
              ) : (
                <>
                  <Tooltip title={t("Edit")} placement="leftBottom">
                    <Button
                      type="primary"
                      className="edit-button"
                      icon={<EditOutlined />}
                      onClick={() => {
                        setEditingIndex(index);
                        setTemp(selected);
                        if (sourceType === "COMPOUND_HIERARCHY") {
                          generateList2Data(selected?.id);
                        }
                      }}
                    />
                  </Tooltip>

                  <Tooltip title={t("Delete")} placement="leftBottom">
                    <Button
                      type="primary"
                      className="cancel-button"
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        const newSelected = [...selectedList];
                        newSelected.splice(index, 1);
                        setSelectedlist([...newSelected]);
                        setVal(newSelected);
                        onEdit(newSelected);
                        setEditingIndex(null);
                        setTemp(null);
                      }}
                    />
                  </Tooltip>
                </>
              )}
            </div>
          </Flex>
        ))
      )}

      {temp?.isNew && (
        <Flex>
          {isEnrichedVisualization ? (
            <>
              <div style={{ flex: 1, overflow: "auto" }}>
                <StyledDataTable
                  noHeader
                  globalFilterFields={["name", "path"]}
                  excelFileName="compound"
                  filters={filtersList1}
                  setFilters={setFiltersList1}
                  sort={sortList1}
                  withMultiplicity
                  multiplicity={attributes?.multiplicityList1}
                  setSort={setSortList1}
                  height={"50vh"}
                  columns={COLUMNS}
                  data={listData1?.map((item) => ({
                    ...item,
                    disabled: selectedList?.some(
                      (selected) => selected.id === item.value
                    ),
                  }))}
                  alignLeft
                  expandable
                  onRowExpand={(e) => {
                    const permissions = getPermissions(e.data?.permissionsId);
                    if (permissions.includes("VIEW")) {
                      const _expanded = expandedRows || {};
                      _expanded[`${e.data.id}`] = true;
                      setExpandedRows(_expanded);
                      handleNodeExpand(e.data.id);
                    } else {
                      setNoPermissionPopup(true);
                    }
                  }}
                  noDownload
                  onRowCollapse={(e) => {
                    const _expanded = { ...(expandedRows || {}) };
                    delete _expanded[e.data.id];
                    setExpandedRows(_expanded);
                  }}
                  expandedRows={expandedRows}
                  expandCondition={(rowData) => {
                    return rowData?.templateHasAttributes;
                  }}
                  rowExpansionTemplate={rowExpansionTemplate}
                  selected={[{ ...temp }]}
                  setSelected={(selected) => {
                    if (selected) {
                      const index = selected.length - 1;
                      const newSelected = { ...temp };
                      newSelected.pathName = selected[0]?.pathName;
                      newSelected.templateId = selected[0]?.templateId;
                      newSelected.id = selected[index]?.value || null;
                      newSelected.name = selected[index]?.label || null;
                      newSelected.permissionsId =
                        selected[index]?.permissionsId;
                      newSelected.templateHasAttributes =
                        selected[index]?.templateHasAttributes || false;
                      newSelected.value = newSelected.value || [];
                      setTemp(newSelected);
                    } else {
                      setTemp(null);
                    }
                  }}
                />
              </div>
              <div style={{ flex: 1, overflow: "auto" }}>
                <StyledDataTable
                  noHeader
                  globalFilterFields={["name", "path"]}
                  excelFileName="compound"
                  withMultiplicity
                  multiplicity={attributes?.multiplicityList2}
                  filters={filtersList2}
                  setFilters={setFiltersList2}
                  sort={sortList2}
                  setSort={setSortList2}
                  height={"50vh"}
                  columns={COLUMNS}
                  data={
                    sourceType === "COMPOUND_HIERARCHY"
                      ? listData2?.filter((list) => list.parentId === temp.id)
                      : listData2
                  }
                  alignLeft
                  expandable
                  onRowExpand={(e) => {
                    const permissions = getPermissions(e.data?.permissionsId);
                    if (permissions.includes("VIEW")) {
                      const _expanded = expandedRows || {};
                      _expanded[`${e.data.id}`] = true;
                      setExpandedRows(_expanded);
                      handleNodeExpand(e.data.id);
                    } else {
                      setNoPermissionPopup(true);
                    }
                  }}
                  noDownload
                  onRowCollapse={(e) => {
                    const _expanded = { ...(expandedRows || {}) };
                    delete _expanded[e.data.id];
                    setExpandedRows(_expanded);
                  }}
                  expandedRows={expandedRows}
                  expandCondition={(rowData) => {
                    return rowData?.templateHasAttributes;
                  }}
                  rowExpansionTemplate={rowExpansionTemplate}
                  selected={temp.value}
                  setSelected={(selected) => {
                    const newSelected = { ...temp };
                    const newChildrens = selected?.map((i) => {
                      return {
                        id: i?.value || i?.id,
                        pathName: i?.pathName,
                        templateId: i?.templateId,
                        name: i.label || i?.name,
                        templateHasAttributes: i.templateHasAttributes,
                        permissionsId: i?.permissionsId,
                      };
                    });
                    newSelected.value = newChildrens;

                    setTemp(newSelected);
                  }}
                />
              </div>
            </>
          ) : (
            <>
              <Select
                defaultOpen
                autoFocus
                allowClear
                options={listData1?.map((item) => ({
                  ...item,
                  title: null,
                  disabled: selectedList?.some(
                    (selected) => selected.id === item.value
                  ),
                }))}
                value={temp.id}
                onChange={(_, item: any) => {
                  if (sourceType === "COMPOUND_HIERARCHY") {
                    generateList2Data(item.value);
                  }

                  const newSelected = { ...temp };
                  newSelected.id = item.value;
                  newSelected.pathName = item?.pathName;
                  newSelected.templateId = item?.templateId;
                  newSelected.name = item.label;
                  newSelected.templateHasAttributes =
                    item.templateHasAttributes;
                  newSelected.permissionsId = item?.permissionsId;

                  setTemp(newSelected);
                }}
                showSearch
                filterOption={(input, option) => {
                  return (option?.label?.toLowerCase() ?? "").includes(
                    input.toLowerCase()
                  );
                }}
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
              />

              <Select
                mode="multiple"
                allowClear
                maxCount={maxCountForList2}
                options={listData2?.map((item) => {
                  return { ...item, title: null };
                })}
                value={temp?.value?.map((item) => item.id) || []}
                onChange={(_, item: any) => {
                  const newSelected = { ...temp };
                  const newChildrens = item?.map((i) => {
                    return {
                      pathName: i?.pathName,
                      templateId: i?.templateId,
                      id: i.value,
                      name: i.label,
                      templateHasAttributes: i.templateHasAttributes,
                      permissionsId: i?.permissionsId,
                    };
                  });
                  newSelected.value = newChildrens;
                  setTemp(newSelected);
                }}
                showSearch
                filterOption={(input, option) => {
                  return (option?.label?.toLowerCase() ?? "").includes(
                    input.toLowerCase()
                  );
                }}
                filterSort={(optionA, optionB) =>
                  (optionA?.label ?? "")
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? "").toLowerCase())
                }
              />
            </>
          )}

          <div className="actions">
            <Tooltip
              title={
                validateMultiplicity() ? t("Invalid multiplicity") : t("Save")
              }
            >
              <Button
                type="primary"
                icon={<i className="pi pi-save" />}
                className="save-btn"
                disabled={validateMultiplicity()}
                onClick={() => {
                  const newSelected = [...selectedList];
                  const newTemp = { ...temp };
                  delete newTemp["isNew"];
                  newSelected.push(newTemp);
                  setSelectedlist([...newSelected]);
                  setVal(newSelected);
                  onEdit(newSelected);
                  setEditingIndex(null);
                  setTemp(null);
                }}
              />
            </Tooltip>

            <Tooltip title={t("Cancel")}>
              <Button
                type="primary"
                className="cancel-btn"
                icon={<i className="pi pi-ban" />}
                onClick={() => {
                  if (temp.isNew) {
                    setEditingIndex(null);
                    setTemp(null);
                  }
                  setTemp(null);
                  setEditingIndex(null);
                }}
              />
            </Tooltip>
          </div>
        </Flex>
      )}

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}

      {noPermissionPopup && (
        <NoPermissionsModal
          visible={noPermissionPopup}
          onHide={() => {
            setNoPermissionPopup(false);
          }}
        />
      )}
    </Wrapper>
  );
};

export { EditCompound };

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

interface ISelected {
  id: number;
  name: number;
  value: {
    id: number;
    name: string;
    templateHasAttributes: boolean;
    permissionsId: number;
  }[];
  templateHasAttributes: boolean;
  permissionsId: number;
}

type ITemplateType = {
  id: number;
};

interface ICompound {
  compoundSourceType: {
    string: string;
  };
  nameList1: string;
  nameList2: string;
  templates1: ITemplateType[];
  templates2: ITemplateType[];
  multiplicityList1: string;
  multiplicityList2: string;
  visualization: string;
  parent1: number;
  parent2: number;
}

const Wrapper = styled.div`
  margin: -6px;
  padding: 6px 6px 1px 12px;
  background-color: var(--color-light);

  & .edit-button {
    background-color: white;
    color: var(--color-text);
    width: fit-content !important;
    margin: auto;
    &:hover {
      background: white !important;
      color: var(--color-text) !important;
    }
  }

  & .cancel-button {
    background-color: white !important;
    color: red;
    width: fit-content !important;
    margin: auto;
    &:hover {
      background: white !important;
      color: red !important;
    }
  }
  & .no-data {
    padding: 4px 6px;
    font-size: 12px;
  }
  & .ant-tree-switcher {
    display: none;
  }

  & .ant-select-selector {
    min-height: 28px !important;
    border-radius: 0px;
    font-size: 12px;
  }
`;

const Flex = styled.div`
  display: flex;
  gap: 6px;
  padding-top: 5px;
  padding-bottom: 5px;
  background-color: white;
  padding: 4px;
  margin-bottom: 6px;
  align-items: stretch;

  & .save-btn {
    background-color: white;
    color: green;
    & i {
      font-size: 14px;
    }
    &:hover {
      background-color: #fff !important;
      opacity: 0.8;
      color: green !important;
    }
  }
  & .cancel-btn {
    background-color: #fff;
    color: #d3a706;
    & i {
      font-size: 14px;
    }
    &:hover {
      background-color: #fff !important;
      color: #d3a706 !important;
      opacity: 0.8;
    }
  }
  & .add-new {
    background-color: white;
    border: 1px solid green;
    color: green;
    &:hover {
      background-color: white !important;
      color: green !important;
      opacity: 0.8;
    }
  }
  & .p-datatable {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }

  & .title-container {
    width: fit-content;
  }
  & .hyperlink {
    cursor: pointer;
    /* border: 1px solid #eee; */
    overflow: auto;

    & .ag-root-wrapper {
      border-radius: 0px;
    }
  }

  & .actions {
    width: 50px;
    flex: unset;
    display: flex;
    gap: 3px;

    & button {
      font-size: 12px;
      width: 100%;
      height: 28px;
      box-shadow: none;

      &:disabled {
        color: white;
        opacity: 0.8;
      }
    }
  }
  & .item {
    cursor: pointer;
    padding: 5px 2px;
    display: flex;
    gap: 4px;
    align-items: center;
    flex-direction: row;

    &:hover {
      text-decoration: underline;
      color: #6363d0;
    }
  }

  & > div {
    flex: 1;

    height: fit-content;
    & h6 {
      max-width: 100%;
    }
  }
  & .data-table-wrapper {
    height: auto !important;
  }
`;
