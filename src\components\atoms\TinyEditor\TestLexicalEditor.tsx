import React, { useState } from 'react';
import { TinyEditor } from './index';

export const TestLexicalEditor: React.FC = () => {
  const [value, setValue] = useState('<p>Hello <strong>World</strong>!</p>');
  const [output, setOutput] = useState('');

  const handleChange = (newValue: string) => {
    console.log('Editor changed:', newValue);
    setOutput(newValue);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Lexical Editor Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Editor:</h3>
        <TinyEditor
          value={value}
          onEditorChange={handleChange}
          minimal={false}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Input Value:</h3>
        <textarea
          value={value}
          onChange={(e) => setValue(e.target.value)}
          style={{ width: '100%', height: '100px', fontFamily: 'monospace' }}
        />
      </div>

      <div>
        <h3>Output HTML:</h3>
        <textarea
          value={output}
          readOnly
          style={{ width: '100%', height: '100px', fontFamily: 'monospace' }}
        />
      </div>
    </div>
  );
};
