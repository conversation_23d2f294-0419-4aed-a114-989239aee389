import { styled } from "@linaria/react";
import { Breadcrumb, Dropdown, Tooltip } from "antd";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useTheme } from "../../../utils/useTheme";
import { ReactNode, memo, useEffect, useState } from "react";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { ReactComponent as NewTabIcon } from "../../../assets/newTab.svg";
import { generateMetamodelOptions, getPathname } from "../../../utils";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { setRefreshBreadcrumbs } from "../../../store/features/breadcrumbs";
import { setSelected } from "../../../store/features";
import { routeMap, TEMP_GROUPING_NODE_ID } from "../../../constants";
import { withErrorBoundary } from "../../withErrorBoundary";

interface IBreadCrumbs {
  to?: string;
  title: string;
  allowedChildrens?: any[];
  parentId?: string;
  disabled?: boolean;
  isParent?: boolean;
  id?: string;
  index: number;
  templateName?: string;
  multipleSelect?: boolean;
  displayTemplatesOption?: boolean;
  mockup?: boolean; //TODO remove in future
}
interface Props {
  items?: IBreadCrumbs[];
  extra?: ReactNode;
  setParentBreadcrumbs?: any;
  setAction?: any;
  dropdownOpen?: boolean;
  metamodel?: boolean;
  className?: string;
  onBreadcrumbClick?: any;
}

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

const BreadCrumbComponent = ({
  extra,
  setAction,
  className,
  onBreadcrumbClick,
}: Props) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const params = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [displayBreadcrumbs, setDisplayBreadcrumbs] = useState([]);

  // redux
  const { mask, breadcrumb } = useSelector((state: RootState) => state.sidebar);
  const {
    bottomDrawer: bottomNavigationMask,
    movingMask,
    workingVersion: workingVersionActive,
  } = useSelector((state: RootState) => state.mask);

  const globalPermissions = useSelector(
    (state: RootState) => state.auth.globalPermissions
  );
  const devMode = useSelector(
    (state: RootState) => state.globalSettings.devMode
  );
  const parentBreadcrumbs = useSelector(
    (state: RootState) => state.breadcrumbs.parentBreadcrumbs
  );
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const displayMultipleSelectedOptions = () => {
    if (globalPermissions.includes("TRASH"))
      return [
        {
          label: t("Delete selected"),
          key: "delete-selected",
          icon: <DeleteOutlined />,
          multipleSelect: true,
        },
      ];
    return [];
  };

  const generateDropdown = (allowedChildrens) => {
    const menus = [];
    const sortedAllowedChildrens = [...allowedChildrens]
      .sort((a, b) => a.name.localeCompare(b.name)) // Sort by name
      .reduce((acc, item) => {
        if (item.id === TEMP_GROUPING_NODE_ID) {
          acc.push(item);
        } else {
          acc.unshift(item);
        }
        return acc;
      }, []);

    if (allowedChildrens && globalPermissions.includes("ADD")) {
      sortedAllowedChildrens?.forEach((allowedChild, index) => {
        if (!allowedChild?.inTrash) {
          if (allowedChild?.id === TEMP_GROUPING_NODE_ID && index !== 0) {
            menus.push({ type: "divider" });
          }
          menus.push({
            label: (
              <div className="header-menus">
                <PlusOutlined /> {t("Add")} {allowedChild?.name}
              </div>
            ),
            key: allowedChild?.id,
          });
        }
      });
    }

    if (menus.length > 0) menus.push({ type: "divider" });
    menus.push({
      label: (
        <div className="header-menus">
          <NewTabIcon className="new-tab" /> {t("Open in new tab")}
        </div>
      ),
      key: "open",
    });

    return menus;
  };

  const handleAllowedChildrenClick = (
    { key },
    allowedChildrens,
    parentId,
    id
  ) => {
    if (key === "open") {
      const URL = `${
        window.origin
      }${baseUrl}${window.location.pathname?.replace(
        baseUrl,
        ""
      )}?nodeId=${id}`;
      window.open(URL);
    } else {
      const selectedAllowedChild = allowedChildrens?.find(
        (temp) => temp.id == key
      );
      setAction({
        id: id,
        isOpen: true,
        parentId: parentId,
        templateId: key,
        title: `${t("Add")} ${selectedAllowedChild?.name}`,
        fromHeader: id == parentId,
      });
    }
  };

  const handleMultipleNodeDelete = () => {
    setAction({
      key: "delete-all",
      title: "Delete all",
    });
  };

  const handleTemplateAdd = ({ key }, href) => {
    if (key === "open") {
      window.open(`${window.origin}${baseUrl}${href}`);
    } else {
      setAction({
        id: params?.nodeId,
        key: "add-document-group",
        title: `${t("Add")} ${templatesData[Number(key)]?.name}` || null,
        templateId: key,
        isOpen: true,
        parentId: params?.nodeId,
        fromHeader: true,
      });
    }
  };

  const handleBreadcrumbClick = () => {
    setTimeout(() => {
      dispatch(setRefreshBreadcrumbs(true));
    }, 300);
  };

  useEffect(() => {
    // Deduplicate by id before rendering
    const seen = new Set();
    const deduped = [...parentBreadcrumbs, ...(breadcrumb || [])].filter(
      (item) => {
        if (!item || typeof item.id === "undefined" || seen.has(item.id))
          return false;
        seen.add(item.id);
        return true;
      }
    );
    setDisplayBreadcrumbs(
      deduped.map((item, index) => ({ ...item, index })) as any[]
    );
  }, [parentBreadcrumbs, breadcrumb]);

  const removeBreadcrumbSelected = (index: number) => {
    const matchedRoute = Array.from(routeMap.keys()).find((key) =>
      location.pathname.includes(key)
    );

    if (matchedRoute) {
      navigate(`/settings/${routeMap.get(matchedRoute)}/${params?.nodeId}`);
    } else {
      navigate(`/details/${params?.nodeId}`);
    }
    dispatch(setSelected({ keys: [], info: [] }));
    const newBreadcrumbs = displayBreadcrumbs.slice(0, index + 1);
    setDisplayBreadcrumbs([...newBreadcrumbs]);
  };

  return (
    <TopBar
      theme={theme}
      className={className}
      metamodel={location.pathname.includes("metamodel")}
      id="mainBreadcrumb"
    >
      <div
        style={{ position: "relative" }}
        className={
          mask || bottomNavigationMask || movingMask || workingVersionActive
            ? "inactive"
            : ""
        }
      >
        <Breadcrumb
          separator=">"
          className="main-breadcrumbs"
          items={[...displayBreadcrumbs]}
          itemRender={(route, params) => {
            // Cast to unknown first to avoid TypeScript error
            const item = route as unknown as IBreadCrumbs;
            const path = `${getPathname()}?nodeId=${item.id}`;

            if (item?.disabled) {
              return <span className="disabled">{item?.title}</span>;
            }

            return (
              <Dropdown
                key={item.index}
                menu={{
                  items: item?.multipleSelect
                    ? displayMultipleSelectedOptions()
                    : item?.displayTemplatesOption
                    ? generateMetamodelOptions(
                        templatesData,
                        Number(params?.nodeId) || Number(item?.id),
                        globalPermissions
                      )
                    : generateDropdown(item?.allowedChildrens || []),
                  onClick: item?.multipleSelect
                    ? handleMultipleNodeDelete
                    : item?.displayTemplatesOption
                    ? (e) => handleTemplateAdd(e, item?.to)
                    : (e) => {
                        handleAllowedChildrenClick(
                          e,
                          item?.allowedChildrens || [],
                          item.parentId,
                          item.id
                        );
                      },
                }}
                trigger={["contextMenu"]}
              >
                {/* is last item of breadcrumb */}
                {displayBreadcrumbs?.length - 1 === item.index ? (
                  <Tooltip title={item?.templateName} placement="right">
                    <p style={{ cursor: "pointer" }} className={"active"}>
                      {t(item?.title)}{" "}
                      {/* {item.mockup && (
                        <span className="mockup">{t("(mockup)")}</span>
                      )} */}
                      {devMode && item?.templateName && (
                        <span style={{ fontWeight: "400" }}>
                          ( {item.templateName} )
                        </span>
                      )}
                    </p>
                  </Tooltip>
                ) : item?.id && item?.id === item?.parentId ? (
                  <span
                    className="pointer"
                    onClick={() => {
                      onBreadcrumbClick && onBreadcrumbClick();
                      removeBreadcrumbSelected(item.index);
                    }}
                  >
                    {t(item?.title)}
                  </span>
                ) : item?.to ? (
                  <Link
                    to={item?.to}
                    className="pointer"
                    onClick={() => {
                      handleBreadcrumbClick();
                      onBreadcrumbClick && onBreadcrumbClick();
                    }}
                  >
                    {t(item?.title)}
                  </Link>
                ) : (
                  <Link
                    to={path}
                    onClick={() => {
                      handleBreadcrumbClick();
                      onBreadcrumbClick && onBreadcrumbClick();
                    }}
                  >
                    {t(item?.title)}
                  </Link>
                )}
              </Dropdown>
            );
          }}
        />
      </div>
      {extra && extra}
    </TopBar>
  );
};

export const BreadCrumb = withErrorBoundary(
  memo(BreadCrumbComponent),
  "error.generic"
);

const TopBar = styled.div<{ theme: any; metamodel: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
  min-height: 34px;
  background: ${({ theme, metamodel }) =>
    metamodel ? theme.metamodelBreadcrumbsColor : theme.colorSecondary};
  white-space: pre;
  & .inactive * {
    pointer-events: none;
    opacity: 0.9;
  }
  & .pointer {
    cursor: pointer;
  }
  & .ant-dropdown-open {
    font-weight: 500;
  }
  & .loader {
    color: #fff;
    font-size: 22px;
  }
  & .disabled,
  .ant-dropdown-trigger {
    color: ${({ theme, metamodel }) =>
      metamodel ? theme.metamodelBreadcrumbsFontColor : theme.breadcrumbsColor};
  }

  & .ant-dropdown-trigger:hover {
    color: ${({ theme, metamodel }) =>
      metamodel ? theme.metamodelBreadcrumbsFontColor : theme.breadcrumbsColor};
    opacity: 0.8;
  }

  & .ant-breadcrumb-separator {
    color: ${({ theme, metamodel }) =>
      metamodel ? theme.metamodelBreadcrumbsFontColor : theme.breadcrumbsColor};
  }

  & > span,
  .anticon-more {
    color: ${({ theme, metamodel }) =>
      metamodel ? theme.metamodelBreadcrumbsFontColor : theme.breadcrumbsColor};
    font-size: 19px !important ;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  & .ant-dropdown-trigger a {
    color: ${({ theme }) => theme.breadcrumbsColor};
  }
`;
