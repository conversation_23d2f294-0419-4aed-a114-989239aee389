import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css";
import Editor from "react-simple-code-editor";

const EditSQL = ({ val, onValueChange }) => {
  const sanitizedValue = (val || "").endsWith("\u200B") ? val : val + "\u200B";

  return (
    <Editor
      className="sql-editor"
      tabSize={4}
      value={sanitizedValue}
      onValueChange={(code) => onValueChange(code.replace(/\u200B/g, ""))}
      highlight={(code) => highlight(code || "", languages.js)}
    />
  );
};

export { EditSQL };
