import { styled } from "@linaria/react";
import { Upload } from "antd";
import { Dialog } from "primereact/dialog";
import { useMutation, useQueryClient } from "react-query";
import { uploadAttachment } from "../../../../services";
import { useNotification } from "../../../../utils/functions/customHooks";
import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { memo, useEffect, useState } from "react";
import { GET_APP_PROPERTIES } from "../../../../constants";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";
import { IAppProperties } from "../../../../interfaces";
import { css } from "@linaria/core";

// Add attachment popup for providing upload field

const AddAttachmentBase = ({ visible, onClose, afterUpload, id }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const { contextHolder, showErrorStyledNotification, showErrorNotification } =
    useNotification();

  const [allowedFileType, setAllowedFileType] = useState(null);

  useEffect(() => {
    const appProperties = queryClient.getQueryData(
      GET_APP_PROPERTIES
    ) as IAppProperties[];

    // get allowed file extensions from app properties
    const allowedFileExtensions = appProperties?.find(
      (prop) => prop.key === "allowed.file.extensions.attachments"
    );
    if (allowedFileExtensions) {
      if (allowedFileExtensions?.value) {
        setAllowedFileType(
          allowedFileExtensions?.value
            ?.split(",")
            ?.map((ext) => `.${ext}`)
            ?.join()
        );
      } else {
        setAllowedFileType(null);
      }
    } else {
      setAllowedFileType("image/png, image/jpg, image/jpeg");
    }
  }, []);

  const uploadAttachmentMutation = useMutation(uploadAttachment, {
    onSuccess: () => {
      afterUpload();
      onClose();
    },
    onError: (error: any) => {
      if (error?.data?.code === "EX_FILE_EXTENSIONS") {
        showErrorStyledNotification(
          <span>
            {t("Error in uploading!")}{" "}
            <span style={{ color: "red" }}>{t("Illegal file type")}</span>
          </span>
        );
      } else {
        showErrorNotification("Error in uploading!");
      }
    },
  });

  const handleCustomRequest = async ({ onSuccess, file }: any) => {
    const formData = new FormData();
    formData.append("fileType", "ATTACHMENT");
    formData.append("nodeId", id);
    formData.append("multipartFile", file.originFileObj || file);
    uploadAttachmentMutation.mutate(formData);
    onSuccess("ok");
  };

  return (
    <Dialog
      header={
        <>
          {t("Add attachment")}
          <span className={headerCss}>
            ({t("allowed file types")}:{" "}
            {allowedFileType
              ?.replace(/\.(?=\w)/g, "")
              ?.replace(/,(?=\S)/g, ", ")}
            )
          </span>
        </>
      }
      headerStyle={{ fontSize: 14, fontWeight: 500 }}
      visible={visible}
      onHide={onClose}
      style={{ width: "50%" }}
    >
      <Wrapper>
        {contextHolder}
        <Upload
          listType="picture-card"
          showUploadList={false}
          customRequest={handleCustomRequest}
          accept={allowedFileType}
          disabled={!allowedFileType}
        >
          <div>
            {uploadAttachmentMutation.isLoading ? (
              <LoadingOutlined />
            ) : (
              <PlusOutlined />
            )}
            <div style={{ marginTop: 8 }}>{t("Upload")}</div>
          </div>
        </Upload>
      </Wrapper>
    </Dialog>
  );
};

const AddAttachment = withErrorBoundary(
  memo(AddAttachmentBase),
  "error.generic"
);

export { AddAttachment };

const Wrapper = styled.div`
  & .ant-upload {
    width: 100% !important;
  }
`;

const headerCss = css`
  font-size: 12px;
  margin-left: 5px;
  color: black;
  font-weight: 400;
`;
