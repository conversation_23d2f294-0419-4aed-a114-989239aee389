import {
  AppstoreOutlined,
  BarsOutlined,
  CommentOutlined,
  DeleteOutlined,
  HistoryOutlined,
  MessageFilled,
  PlusOutlined,
  PushpinFilled,
} from "@ant-design/icons";
import { styled } from "@linaria/react";
import { Segmented } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  addSection,
  removeSection,
  setHasShortcut,
  setHomeLayout,
} from "../../../store/features/localSettings";
import { RootState } from "../../../store";

const HomeLayoutOptions = ({ detectChange }) => {
  const { t } = useTranslation();
  const [layoutOptions, setLayoutOptions] = useState([]);
  const dispatch = useDispatch();
  const { sections, homeLayoutType } = useSelector(
    (root: RootState) => root.localSettings
  );

  const addNewSection = (key) => {
    dispatch(setHasShortcut(false));
    dispatch(addSection(key));
    detectChange();
  };

  useEffect(() => {
    if (sections.length === 0) {
      setLayoutOptions(LAYOUT);
    } else {
      let allLayouts = [...LAYOUT];
      const newLayouts = [];
      sections.forEach((selectedItem: string) => {
        allLayouts = allLayouts?.filter((item) => item.key !== selectedItem);
        const selected = LAYOUT.find((item) => item.key === selectedItem);
        newLayouts.push(selected);
      });
      setLayoutOptions([...newLayouts, ...allLayouts]);
    }
  }, [sections]);

  return (
    <SettingsGrid>
      {sections.length > 0 && (
        <Segmented
          value={homeLayoutType}
          onChange={(value: string) => {
            dispatch(setHomeLayout(value));
            detectChange();
          }}
          options={[
            {
              value: "kanban",
              icon: <AppstoreOutlined />,
            },
            {
              value: "list",
              icon: <BarsOutlined />,
            },
          ]}
        />
      )}
      <div className="grid">
        {layoutOptions.map((item) => {
          const isSelected = sections.includes(item.key);
          return (
            <div
              key={item.key}
              className={`card ${isSelected ? "selected" : ""}`}
            >
              {item.icon} {t(item.title)}
              {isSelected ? (
                <div
                  className="overlay overlay-delete"
                  onClick={() => {
                    detectChange();
                    dispatch(removeSection(item.key));
                  }}
                >
                  <DeleteOutlined />
                </div>
              ) : (
                <div
                  className="overlay overlay-plus"
                  onClick={() => addNewSection(item.key)}
                >
                  <PlusOutlined />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </SettingsGrid>
  );
};

const LAYOUT = [
  {
    title: "Pinned",
    key: "pinned",
    icon: <PushpinFilled />,
  },
  {
    title: "Comments",
    key: "comments",
    icon: <CommentOutlined />,
  },
  {
    title: "Messages",
    key: "messages",
    icon: <MessageFilled />,
  },
  {
    title: "History",
    key: "history",
    icon: <HistoryOutlined />,
  },
];

export { HomeLayoutOptions };

const SettingsGrid = styled.div`
  padding-bottom: 10px;

  & .selected {
    background-color: #5690c0 !important;
  }
  & .ant-segmented {
    margin-left: auto;
    display: flex;
    width: fit-content;
    margin-bottom: 10px;

    & svg {
      font-size: 15px;
    }
  }

  & .grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
  & .card {
    min-width: 100px;
    background-color: #185d9c;
    color: #fff;
    cursor: pointer;
    padding: 12px 20px;
    border-radius: 4px;
    text-align: center;
    position: relative;
    &:hover .overlay {
      opacity: 1;
      transition: all 0.3s ease-in;
    }
  }

  & .switch {
    display: flex;
    justify-content: space-between;
    margin-top: 14px;
    color: #185d9c;
  }

  & .overlay {
    position: absolute;
    opacity: 0;
    transition: all 0.3s ease-in;
    inset: 0px;
    display: flex;

    justify-content: center;

    & svg {
      font-size: 18px;
    }
  }

  & .overlay-delete {
    background: #ff0000d4;
  }

  & .overlay-plus {
    background: #008000bd;
  }
`;
