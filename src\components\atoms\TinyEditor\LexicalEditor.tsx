import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin';
import { TRANSFORMERS } from '@lexical/markdown';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { $generateHtmlFromNodes, $generateNodesFromDOM } from '@lexical/html';
import { $getRoot, $insertNodes } from 'lexical';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { TableCellNode, TableNode, TableRowNode } from '@lexical/table';
import { ListItemNode, ListNode } from '@lexical/list';
import { CodeHighlightNode, CodeNode } from '@lexical/code';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { ToolbarPlugin } from './plugins/ToolbarPlugin';
import { theme } from './theme/EditorTheme';
import './styles/LexicalEditor.css';

interface LexicalEditorProps {
  value?: string;
  onChange?: (html: string) => void;
  minimal?: boolean;
  noFocus?: boolean;
  disabled?: boolean;
  onInsertIframe?: () => void;
  editorProperties?: any;
  language?: string;
}

interface LexicalEditorRef {
  insertHTML: (html: string) => void;
  focus: () => void;
}

const nodes = [
  HeadingNode,
  ListNode,
  ListItemNode,
  QuoteNode,
  CodeNode,
  CodeHighlightNode,
  TableNode,
  TableCellNode,
  TableRowNode,
  AutoLinkNode,
  LinkNode,
];

function MyOnChangePlugin({ onChange }: { onChange: (html: string) => void }) {
  const [editor] = useLexicalComposerContext();
  
  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const htmlString = $generateHtmlFromNodes(editor, null);
        onChange(htmlString);
      });
    });
  }, [editor, onChange]);

  return null;
}

function InitialValuePlugin({ value }: { value?: string }) {
  const [editor] = useLexicalComposerContext();
  const [isFirstRender, setIsFirstRender] = useState(true);

  useEffect(() => {
    if (value && isFirstRender) {
      editor.update(() => {
        const parser = new DOMParser();
        const dom = parser.parseFromString(value, 'text/html');
        const nodes = $generateNodesFromDOM(editor, dom);
        $getRoot().select();
        $insertNodes(nodes);
      });
      setIsFirstRender(false);
    }
  }, [editor, value, isFirstRender]);

  return null;
}

const LexicalEditorInner = forwardRef<LexicalEditorRef, LexicalEditorProps>(
  ({ value, onChange, minimal, noFocus, disabled, onInsertIframe, editorProperties }, ref) => {
    const [editor] = useLexicalComposerContext();

    useImperativeHandle(ref, () => ({
      insertHTML: (html: string) => {
        editor.update(() => {
          const parser = new DOMParser();
          const dom = parser.parseFromString(html, 'text/html');
          const nodes = $generateNodesFromDOM(editor, dom);
          $insertNodes(nodes);
        });
      },
      focus: () => {
        editor.focus();
      },
    }));

    const handleChange = useCallback((html: string) => {
      if (onChange) {
        onChange(html);
      }
    }, [onChange]);

    return (
      <div className="lexical-editor-container">
        <ToolbarPlugin 
          minimal={minimal} 
          onInsertIframe={onInsertIframe}
          editorProperties={editorProperties}
        />
        <div className="lexical-editor-inner">
          <RichTextPlugin
            contentEditable={
              <ContentEditable 
                className="lexical-content-editable" 
                readOnly={disabled}
              />
            }
            placeholder={
              <div className="lexical-placeholder">
                Enter some text...
              </div>
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          <HistoryPlugin />
          {!noFocus && <AutoFocusPlugin />}
          <LinkPlugin />
          <ListPlugin />
          <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
          <MyOnChangePlugin onChange={handleChange} />
          <InitialValuePlugin value={value} />
        </div>
      </div>
    );
  }
);

export const LexicalEditor = forwardRef<LexicalEditorRef, LexicalEditorProps>(
  (props, ref) => {
    const initialConfig = {
      namespace: 'LexicalEditor',
      theme,
      onError: (error: Error) => {
        console.error('Lexical error:', error);
      },
      nodes,
    };

    return (
      <LexicalComposer initialConfig={initialConfig}>
        <LexicalEditorInner {...props} ref={ref} />
      </LexicalComposer>
    );
  }
);

LexicalEditor.displayName = 'LexicalEditor';
LexicalEditorInner.displayName = 'LexicalEditorInner';
