import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { $generateHtmlFromNodes, $generateNodesFromDOM } from '@lexical/html';
import { $getRoot, $insertNodes, $createParagraphNode, $createTextNode } from 'lexical';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { ToolbarPlugin } from './plugins/ToolbarPlugin';
import { theme } from './theme/EditorTheme';
import './styles/LexicalEditor.css';

interface LexicalEditorProps {
  value?: string;
  onChange?: (html: string) => void;
  minimal?: boolean;
  noFocus?: boolean;
  disabled?: boolean;
  onInsertIframe?: () => void;
  editorProperties?: any;
  language?: string;
}

interface LexicalEditorRef {
  insertHTML: (html: string) => void;
  focus: () => void;
}

const nodes = [
  HeadingNode,
  ListNode,
  ListItemNode,
  QuoteNode,
  AutoLinkNode,
  LinkNode,
];

function OnChangePlugin({ onChange }: { onChange: (html: string) => void }) {
  const [editor] = useLexicalComposerContext();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const unregister = editor.registerUpdateListener(({ editorState }) => {
      // Debounce the onChange to prevent infinite loops
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        try {
          editorState.read(() => {
            const htmlString = $generateHtmlFromNodes(editor, null);
            onChange(htmlString);
          });
        } catch (error) {
          console.error('Error generating HTML:', error);
        }
      }, 100);
    });

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      unregister();
    };
  }, [editor, onChange]);

  return null;
}

function InitialValuePlugin({ value }: { value?: string }) {
  const [editor] = useLexicalComposerContext();
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    if (!initialized && value && typeof value === 'string' && value.trim()) {
      try {
        editor.update(() => {
          const root = $getRoot();
          root.clear();

          if (value.includes('<') && value.includes('>')) {
            // It's HTML content
            const parser = new DOMParser();
            const dom = parser.parseFromString(value, 'text/html');
            const nodes = $generateNodesFromDOM(editor, dom);
            root.append(...nodes);
          } else {
            // It's plain text
            const paragraph = $createParagraphNode();
            paragraph.append($createTextNode(value));
            root.append(paragraph);
          }
        });
      } catch (error) {
        console.error('Error setting initial value:', error);
        // Fallback to plain text
        editor.update(() => {
          const root = $getRoot();
          root.clear();
          const paragraph = $createParagraphNode();
          paragraph.append($createTextNode(String(value)));
          root.append(paragraph);
        });
      }
      setInitialized(true);
    }
  }, [editor, value, initialized]);

  return null;
}

const LexicalEditorInner = forwardRef<LexicalEditorRef, LexicalEditorProps>(
  ({ value, onChange, minimal, noFocus, disabled, onInsertIframe, editorProperties }, ref) => {
    const [editor] = useLexicalComposerContext();

    useImperativeHandle(ref, () => ({
      insertHTML: (html: string) => {
        try {
          editor.update(() => {
            const parser = new DOMParser();
            const dom = parser.parseFromString(html, 'text/html');
            const nodes = $generateNodesFromDOM(editor, dom);
            $insertNodes(nodes);
          });
        } catch (error) {
          console.error('Error inserting HTML:', error);
        }
      },
      focus: () => {
        try {
          editor.focus();
        } catch (error) {
          console.error('Error focusing editor:', error);
        }
      },
    }));

    const handleChange = useCallback((html: string) => {
      if (onChange && typeof onChange === 'function') {
        onChange(html);
      }
    }, [onChange]);

    return (
      <div className="lexical-editor-container">
        {!minimal && (
          <ToolbarPlugin
            minimal={minimal}
            onInsertIframe={onInsertIframe}
            editorProperties={editorProperties}
          />
        )}
        <div className="lexical-editor-inner">
          <RichTextPlugin
            contentEditable={
              <ContentEditable
                className="lexical-content-editable"
                readOnly={disabled}
              />
            }
            placeholder={
              <div className="lexical-placeholder">
                Enter some text...
              </div>
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          <HistoryPlugin />
          {!noFocus && <AutoFocusPlugin />}
          <LinkPlugin />
          <ListPlugin />
          {onChange && <OnChangePlugin onChange={handleChange} />}
          <InitialValuePlugin value={value} />
        </div>
      </div>
    );
  }
);

export const LexicalEditor = forwardRef<LexicalEditorRef, LexicalEditorProps>(
  (props, ref) => {
    const initialConfig = {
      namespace: 'LexicalEditor',
      theme,
      onError: (error: Error) => {
        console.error('Lexical error:', error);
        // Don't throw the error, just log it
      },
      nodes,
      editorState: null, // Start with empty state
    };

    return (
      <div className="lexical-wrapper">
        <LexicalComposer initialConfig={initialConfig}>
          <LexicalEditorInner {...props} ref={ref} />
        </LexicalComposer>
      </div>
    );
  }
);

LexicalEditor.displayName = 'LexicalEditor';
LexicalEditorInner.displayName = 'LexicalEditorInner';
