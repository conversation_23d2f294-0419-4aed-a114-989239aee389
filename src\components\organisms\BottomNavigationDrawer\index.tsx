import {
  ExpandAltOutlined,
  ShrinkOutlined,
  FullscreenOutlined,
  MinusOutlined,
} from "@ant-design/icons";
import { styled } from "@linaria/react";
import { useGenerateBottomNavigationMenu } from "../../../constants/menus/bottomNavigationMenu";
import { useTheme } from "../../../utils/useTheme";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { MyTooltip } from "../../atoms";
import { DraggableTabs, ResizableDiv } from "../../molecules";
import { useLocation, useSearchParams } from "react-router-dom";
import {
  GET_COUNTERS,
  GET_NODE_ATTRIBUTES_DETAILS,
  GET_SETTINGS_VARIABLE,
  OBJECT_TEMPLATE_ID,
} from "../../../constants";
import { useQuery, useQueryClient } from "react-query";
import { getCounters } from "../../../services/counters";
import i18next from "i18next";
import { getNodeDetails } from "../../../services/node";
import {
  setBottomNavbarOpen,
  setSelectedBottomNavbar,
} from "../../../store/features/navigation";
import { ISettingsVariable, ITemplates } from "../../../interfaces";
import { useFlags } from "../../../utils/functions/customHooks";
import { withErrorBoundary } from "../../withErrorBoundary";

interface Props {
  id?: any;
  noGraph?: boolean;
  trashcan?: boolean;
  metamodel?: boolean;
  fromModal?: boolean;
  displaySaveCancel: boolean;
  fromMenuCreator?: boolean;
}

const RESIZED_HEIGHT_KEY_TRASHCAN = "bottom-drawer-height-trashcan";
const DRAWER_ACTION_STATE_KEY_TRASHCAN = "bottom-drawer-action-state-trashcan";

const RESIZED_HEIGHT_KEY_NORMAL = "bottom-drawer-height-normal";
const DRAWER_ACTION_STATE_KEY_NORMAL = "bottom-drawer-action-state-normal";
// {"node":{"name":"t1","nodeType":"DATA","templateId":672,"body":[]},"parentId":733629}
const BottomNavigationDrawerBase = memo(
  ({
    noGraph,
    id,
    trashcan,
    fromMenuCreator,
    metamodel,
    fromModal,
    displaySaveCancel,
  }: Props) => {
    const theme = useTheme() as any;
    const [searchParams] = useSearchParams();
    const dispatch = useDispatch();
    const resizableRef = useRef(null);
    const queryClient = useQueryClient();
    const location = useLocation();

    const MIN_HEIGHT = 50; // Ensure enough space for drag handle
    const [height, setHeight] = useState(MIN_HEIGHT);
    const [isFullScreen, setIsFullScreen] = useState(false);
    const [isMinimized, setIsMinimized] = useState(true);

    // Track last resized height for consistent restore/expand
    const [lastResizedHeight, setLastResizedHeight] = useState(
      Number(
        localStorage.getItem(
          trashcan ? RESIZED_HEIGHT_KEY_TRASHCAN : RESIZED_HEIGHT_KEY_NORMAL
        ) || 300
      )
    );
    // Track last valid user-resized height (not min or fullscreen)
    const [lastValidResizedHeight, setLastValidResizedHeight] = useState(
      Number(
        localStorage.getItem(
          trashcan ? RESIZED_HEIGHT_KEY_TRASHCAN : RESIZED_HEIGHT_KEY_NORMAL
        ) || 300
      )
    );
    const [tooltipKey, setTooltipKey] = useState(0);

    const { getFlags } = useFlags();
    const { getBottomNavigationMenus } = useGenerateBottomNavigationMenu();

    const authenticated = useSelector(
      (state: RootState) => state.auth.authenticated
    );
    const { mask, attributeMask } = useSelector(
      (state: RootState) => state.sidebar
    );
    const templatesData = useSelector(
      (state: RootState) => state.templatesStore.templates
    );
    const { trashcanDrawerMask } = useSelector(
      (state: RootState) => state.trash
    );

    const [localOpenState, setLocalOpenState] = useState(false);
    const [localSelectedState, setLocalSelectedState] = useState("");

    const { navigationItems, selectedBottomNavbar, bottomNavbarOpen } =
      useSelector((state: RootState) => state.navigation);

    const ID = id || searchParams.get("nodeId");

    const [showWorkingVersion, setShowWorkingVersion] = useState(false);

    const { data: bodyData } = useQuery(
      [GET_NODE_ATTRIBUTES_DETAILS, ID],
      () => getNodeDetails(ID),
      {
        enabled:
          !!ID &&
          !!metamodel &&
          !searchParams.get("draft") &&
          !!searchParams.get("nodeId") &&
          !!templatesData,
        cacheTime: Infinity,
      }
    );

    const getlastKnowState = () => {
      const actionStateKey = trashcan
        ? DRAWER_ACTION_STATE_KEY_TRASHCAN
        : DRAWER_ACTION_STATE_KEY_NORMAL;
      const heightStateKey = trashcan
        ? RESIZED_HEIGHT_KEY_TRASHCAN
        : RESIZED_HEIGHT_KEY_NORMAL;
      const savedHeight = localStorage.getItem(heightStateKey);
      const savedState = localStorage.getItem(actionStateKey);
      return {
        height: savedHeight,
        state: savedState,
      };
    };

    useEffect(() => {
      setLocalSelectedState("");
      setLocalOpenState(false);
    }, []);

    useEffect(() => {
      if (metamodel && bodyData?.bitFlag) {
        const flags = getFlags(bodyData?.bitFlag) as string[];
        setShowWorkingVersion(
          flags?.includes("EDITMODE") &&
            bodyData?.templateId === OBJECT_TEMPLATE_ID
        );
      } else {
        setShowWorkingVersion(false);
      }
    }, [bodyData]);

    const settingsVariables = queryClient.getQueryData(
      GET_SETTINGS_VARIABLE
    ) as ISettingsVariable;

    const { data: countersData } = useQuery(
      [GET_COUNTERS, ID],
      () => getCounters(ID),
      {
        enabled: !searchParams.get("draft") && !!ID && !!authenticated,
      }
    );

    const selectedNodeTemplate = useMemo(() => {
      return (
        templatesData && (templatesData[bodyData?.templateId] as ITemplates)
      );
    }, [templatesData, bodyData?.templateId]);

    const IS_MODEL = location.pathname.includes("details");

    // Utility to check if path matches any of the restricted sections
    const shouldHideGraphTab = useMemo(() => {
      const path = location.pathname.toLowerCase();
      return (
        path.includes("roles") ||
        path.includes("actions") ||
        path.includes("metamodel")
      );
    }, [location.pathname]);

    // Utility to check if we're in authors context
    const isAuthorsContext = useMemo(() => {
      return location.pathname.toLowerCase().includes("authors");
    }, [location.pathname]);

    // Helper to filter tabs based on context
    const filterTabs = (tabs) => {
      // For other contexts: apply existing graph tab filtering
      return shouldHideGraphTab
        ? tabs.filter((tab) => tab.key !== "graph")
        : tabs;
    };

    const [items, setItems] = useState(
      filterTabs(
        getBottomNavigationMenus(
          noGraph,
          ID,
          countersData,
          showWorkingVersion,
          bodyData?.templateId === OBJECT_TEMPLATE_ID,
          trashcan,
          displaySaveCancel,
          fromModal ? localOpenState : bottomNavbarOpen,
          settingsVariables?.CONNECTOR_EXTRACTOR_TEMPLATE_IDS?.includes(
            bodyData?.templateId
          ),
          templatesData ? selectedNodeTemplate?.testDqm : null, //for dqm tabs
          selectedNodeTemplate?.actions?.includes(4), //for logs
          IS_MODEL || fromMenuCreator || isAuthorsContext, // Show permissions for authors
          fromMenuCreator //from menu creator
        )
      )
    );

    useEffect(() => {
      setItems(
        filterTabs(
          getBottomNavigationMenus(
            noGraph,
            id || searchParams.get("nodeId"),
            countersData,
            showWorkingVersion,
            bodyData?.templateId === OBJECT_TEMPLATE_ID,
            trashcan,
            displaySaveCancel,
            fromModal ? localOpenState : bottomNavbarOpen,
            settingsVariables?.CONNECTOR_EXTRACTOR_TEMPLATE_IDS?.includes(
              bodyData?.templateId
            ),
            templatesData ? selectedNodeTemplate?.testDqm : null,
            selectedNodeTemplate?.actions?.includes(4),
            IS_MODEL || fromMenuCreator || isAuthorsContext, // Show permissions for authors
            fromMenuCreator
          )
        )
      );
    }, [
      countersData,
      navigationItems,
      i18next.language,
      showWorkingVersion,
      displaySaveCancel,
      bottomNavbarOpen,
      localOpenState,
      selectedNodeTemplate,
      fromMenuCreator,
      shouldHideGraphTab,
      isAuthorsContext,
    ]);

    const handleTabChange = (value) => {
      if (isMinimized) {
        // If minimized, expand the drawer with the selected tab
        handleExpand(value);
      } else {
        // If already expanded, just set the selected tab
        if (fromModal) {
          setLocalSelectedState(value);
          setLocalOpenState(true);
        } else {
          dispatch(setSelectedBottomNavbar(value));
          dispatch(setBottomNavbarOpen(true));
        }
      }
    };

    const updateLocalStorage = (actionState, height) => {
      const actionStateKey = trashcan
        ? DRAWER_ACTION_STATE_KEY_TRASHCAN
        : DRAWER_ACTION_STATE_KEY_NORMAL;
      const heightStateKey = trashcan
        ? RESIZED_HEIGHT_KEY_TRASHCAN
        : RESIZED_HEIGHT_KEY_NORMAL;

      localStorage.setItem(actionStateKey, actionState);
      localStorage.setItem(heightStateKey, height);
    };

    // ---
    // Update both previousHeight and lastResizedHeight on resize
    const handleResize = (_event, _direction, refToElement) => {
      const parentHeight =
        refToElement.parentElement.parentElement.clientHeight;
      const resizableHeight = refToElement.clientHeight;
      setHeight(resizableHeight);
      setLastResizedHeight(resizableHeight);
      // Only update lastValidResizedHeight if not at min or fullscreen
      if (
        resizableHeight > MIN_HEIGHT + 2 &&
        resizableHeight < parentHeight - 2
      ) {
        setLastValidResizedHeight(resizableHeight);
      }
      if (resizableHeight >= parentHeight - 2) {
        setIsFullScreen(true);
        setIsMinimized(false);
        updateLocalStorage("fullscreen", resizableHeight);
      } else if (resizableHeight <= MIN_HEIGHT + 2) {
        setIsMinimized(true);
        setIsFullScreen(false);
        updateLocalStorage("minimized", resizableHeight);
      } else {
        setIsFullScreen(false);
        setIsMinimized(false);
        updateLocalStorage("normal", resizableHeight);
      }
      setTooltipKey((k) => k + 1);
    };

    const handleBrowserZoom = () => {
      if (resizableRef.current) {
        const parentHeight = resizableRef.current.parentElement.clientHeight;
        const resizableHeight = resizableRef.current.clientHeight;

        setHeight(resizableHeight);

        if (resizableHeight >= parentHeight - 2) {
          setIsFullScreen(true);
          setIsMinimized(false);
          updateLocalStorage("fullscreen", resizableHeight);
        } else if (resizableHeight <= MIN_HEIGHT + 2) {
          setIsMinimized(true);
          setIsFullScreen(false);
          updateLocalStorage("minimized", resizableHeight);
        } else {
          setIsFullScreen(false);
          setIsMinimized(false);
          updateLocalStorage("normal", resizableHeight);
        }
      }

      setTooltipKey((k) => k + 1);
    };

    useEffect(() => {
      window.addEventListener("resize", handleBrowserZoom);

      return () => {
        window.removeEventListener("resize", handleBrowserZoom);
      };
    }, []);

    useEffect(() => {
      const { height, state } = getlastKnowState();
      if (height && state) {
        setHeight(Number(height));
        setLastResizedHeight(Number(height));
        setLastValidResizedHeight(Number(height));
        setTimeout(() => {
          if (state === "minimized") {
            setIsMinimized(true);
            setIsFullScreen(false);
            dispatch(setBottomNavbarOpen(false));
            dispatch(setSelectedBottomNavbar(""));
          } else if (state === "fullscreen") {
            setIsMinimized(false);
            setIsFullScreen(true);
            dispatch(setBottomNavbarOpen(true));
          } else {
            setIsMinimized(false);
            setIsFullScreen(false);
            dispatch(setBottomNavbarOpen(true));
          }
        }, 0);
      } else {
        setHeight(MIN_HEIGHT);
        setLastResizedHeight(MIN_HEIGHT);
        setLastValidResizedHeight(MIN_HEIGHT);
        setIsMinimized(true);
        setIsFullScreen(false);
        dispatch(setBottomNavbarOpen(false));
        dispatch(setSelectedBottomNavbar(""));
      }
      setTooltipKey((k) => k + 1);
    }, []);

    useEffect(() => {
      if (!resizableRef.current) return;
      const parentHeight =
        resizableRef.current.parentElement?.clientHeight || 600;
      if (height >= parentHeight - 2) {
        setIsFullScreen(true);
        setIsMinimized(false);
        updateLocalStorage("fullscreen", height);
      } else if (height <= MIN_HEIGHT + 2) {
        setIsMinimized(true);
        setIsFullScreen(false);
        updateLocalStorage("minimized", height);
      } else {
        setIsFullScreen(false);
        setIsMinimized(false);
        updateLocalStorage("normal", height);
      }

      setTooltipKey((k) => k + 1);
    }, [height]);

    // Watch for programmatic tab selection and expand the drawer if needed
    useEffect(() => {
      const selectedTab = fromModal ? localSelectedState : selectedBottomNavbar;

      if (selectedTab && isMinimized) {
        handleExpand(selectedTab);
      }
    }, [selectedBottomNavbar, localSelectedState]);

    // Expand from minimized
    const handleExpand = (selectedTab = null) => {
      const parent = document.getElementById("details-parent-container");
      if (!parent) return;
      // Use lastValidResizedHeight if lastResizedHeight is at an edge
      let restoreHeight = lastResizedHeight;
      if (
        restoreHeight <= MIN_HEIGHT + 2 ||
        restoreHeight >= parent.clientHeight - 2
      ) {
        restoreHeight =
          lastValidResizedHeight > 300 ? lastValidResizedHeight : 300;
      }
      setHeight(restoreHeight);
      setIsMinimized(false);
      setIsFullScreen(false);
      if (fromModal) {
        setLocalOpenState(true);
        if (selectedTab) {
          setLocalSelectedState(selectedTab);
        } else if (!localSelectedState) {
          setLocalSelectedState("history");
        }
      } else {
        dispatch(setBottomNavbarOpen(true));
        if (selectedTab) {
          dispatch(setSelectedBottomNavbar(selectedTab));
        } else if (!selectedBottomNavbar) {
          dispatch(setSelectedBottomNavbar("history"));
        }
      }
      setTooltipKey((k) => k + 1);
    };

    // Maximize to full screen
    const handleFullScreen = () => {
      const parent = document.getElementById("details-parent-container");
      if (!parent) return;
      setHeight(parent.clientHeight);
      setIsFullScreen(true);
      setIsMinimized(false);
      updateLocalStorage("fullscreen", parent.clientHeight);
      setTooltipKey((k) => k + 1);
    };

    // Restore from full screen or minimized
    const handleRestore = () => {
      const parent = document.getElementById("details-parent-container");
      if (!parent) return;
      // Use lastValidResizedHeight if lastResizedHeight is at an edge
      let restoreHeight = lastResizedHeight;
      if (
        restoreHeight <= MIN_HEIGHT + 2 ||
        restoreHeight >= parent.clientHeight - 2
      ) {
        restoreHeight =
          lastValidResizedHeight > 300 ? lastValidResizedHeight : 300;
      }
      setHeight(restoreHeight);
      setIsFullScreen(false);
      setIsMinimized(false);
    };

    // Collapse (minimize)
    const handleCollapse = () => {
      setHeight(MIN_HEIGHT);
      setIsMinimized(true);
      setIsFullScreen(false);
      if (fromModal) {
        setLocalOpenState(false);
        setLocalSelectedState("");
      } else {
        dispatch(setBottomNavbarOpen(false));
        dispatch(setSelectedBottomNavbar(""));
      }
      updateLocalStorage("minimized", MIN_HEIGHT);
      setTooltipKey((k) => k + 1);
    };

    // if (fromModal ? localOpenState : bottomNavbarOpen) {
    // Drawer is open (not minimized)
    return (
      <div ref={resizableRef} className="drawer">
        <ResizableDiv
          height={height}
          defaultHeight="300px"
          resize="top"
          onResize={handleResize}
          className={`bottom-navbar ${
            trashcanDrawerMask ? "trash-bottom-drawer-mask" : ""
          }`}
          // Remove saveHeightToLocalStorage, we now control storage from parent
          stateKey={
            trashcan
              ? DRAWER_ACTION_STATE_KEY_TRASHCAN
              : DRAWER_ACTION_STATE_KEY_NORMAL
          }
          minHeight={MIN_HEIGHT}
        >
          <Wrapper
            theme={theme}
            trashcan={trashcan}
            metamodel={location.pathname?.includes("/metamodel")}
            style={{ height: "100%" }}
          >
            {(mask || attributeMask) && !fromMenuCreator && (
              <Mask className="mask" />
            )}
            <DraggableTabs
              items={items}
              onTabClick={handleTabChange}
              setItems={setItems}
              fromModal={fromModal}
              localActiveKey={localSelectedState}
              tabBarExtraContent={
                <div className="actions">
                  {isMinimized ? (
                    <>
                      <MyTooltip key={tooltipKey + 2} title="Full screen">
                        <FullscreenOutlined
                          onClick={() => {
                            handleFullScreen();
                            setTooltipKey((k) => k + 1);
                          }}
                        />
                      </MyTooltip>
                      <MyTooltip key={tooltipKey} title="Expand">
                        <ExpandAltOutlined
                          onClick={() => {
                            handleExpand();
                            setTooltipKey((k) => k + 1);
                          }}
                        />
                      </MyTooltip>
                    </>
                  ) : (
                    <>
                      {isFullScreen ? (
                        <MyTooltip key={tooltipKey + 1} title="Restore">
                          <ShrinkOutlined
                            onClick={() => {
                              handleRestore();
                              setTooltipKey((k) => k + 1);
                            }}
                          />
                        </MyTooltip>
                      ) : (
                        <MyTooltip key={tooltipKey + 2} title="Full screen">
                          <FullscreenOutlined
                            onClick={() => {
                              handleFullScreen();
                              setTooltipKey((k) => k + 1);
                            }}
                          />
                        </MyTooltip>
                      )}
                      <MyTooltip key={tooltipKey + 3} title="Collapse">
                        <MinusOutlined
                          onClick={() => {
                            handleCollapse();
                            setTooltipKey((k) => k + 1);
                          }}
                        />
                      </MyTooltip>
                    </>
                  )}
                </div>
              }
            />
          </Wrapper>
        </ResizableDiv>
      </div>
    );
    // }

    // Minimized state: show only Expand
    // return (
    //   <Wrapper metamodel={metamodel} theme={theme} trashcan={trashcan}>
    //     {(mask || attributeMask) && !fromMenuCreator && (
    //       <Mask className="mask" />
    //     )}
    //     <DraggableTabs
    //       items={items}
    //       onTabClick={handleTabChange}
    //       setItems={setItems}
    //       fromModal={fromModal}
    //       localActiveKey={localSelectedState}
    //       tabBarExtraContent={
    //         <div className="actions">
    //           <MyTooltip title="Expand">
    //             <ExpandAltOutlined onClick={handleExpand} />
    //           </MyTooltip>
    //         </div>
    //       }
    //     />
    //   </Wrapper>
    // );
  }
);

export const BottomNavigationDrawer = withErrorBoundary(
  BottomNavigationDrawerBase,
  "error.generic"
);

const Mask = styled.div`
  height: 100%;
  width: 100%;
  z-index: 1;
`;

const Wrapper = styled.div<{
  theme: any;
  trashcan: boolean;
  metamodel: boolean;
}>`
  border-top: ${({ theme, trashcan, metamodel }) =>
    `3px solid ${
      trashcan
        ? theme.trashBreadcrumbsColor
        : metamodel
        ? theme.metamodelBreadcrumbsColor
        : theme.colorSecondary
    }`};

  & .mask-tabs {
    height: 45px;
    width: 100%;
  }
  & .actions {
    display: flex;
    gap: 15px;
    font-size: 17px;
    color: ${({ theme }) => theme.colorPrimary};

    & button {
      padding: 0px 10px;
      font-size: 13px;
      height: 24px;
      border-radius: 3px;
    }
  }

  & .ant-tabs-nav {
    padding-left: 20px;
    padding-right: 20px;
    margin-bottom: 0px;
  }
  & .ant-tabs,
  .ant-tabs-content,
  .ant-tabs-tabpane {
    height: 100%;
  }
  & .ant-tabs-content-holder {
    flex: 1;
    overflow: hidden;
  }

  & .ant-tabs-extra-content > .anticon {
    font-size: 19px;
    color: ${({ theme }) => theme.colorPrimary};
    cursor: pointer;
  }
  & .ant-tabs-tab {
    & p {
      color: ${({ theme }) => theme.colorPrimary};
      font-size: 13px;
      cursor: pointer;

      & .counter {
        background-color: ${({ theme }) => theme.bgAttributes};
        color: ${({ theme }) => theme.colorPrimary};
        min-width: 23px;
        border-radius: 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        max-height: 15px;
      }

      & .suspect-counter {
        background: #f4020029;
        color: #f70000;
      }
    }
  }
`;
