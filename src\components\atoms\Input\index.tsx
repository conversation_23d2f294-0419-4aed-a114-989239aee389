import { styled } from "@linaria/react";
import { Controller } from "react-hook-form";
import { Input as AntdInput } from "antd";
import { theme } from "../../../utils/theme";
import { useTranslation } from "react-i18next";
import React, { useEffect, useRef } from "react";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

interface Props {
  control: any;
  error?: string;
  label?: string;
  name: string;
  type?: "password" | "textarea" | "passwordWithFeedback";
  horizontal?: boolean;
  defaultValue?: string;
  disabled?: boolean;
}

const InputBase = ({
  control,
  label,
  error,
  name,
  type,
  horizontal,
  defaultValue,
  disabled,
}: Props) => {
  const { t } = useTranslation();
  const ref = useRef(null);

  useEffect(() => {
    if (ref?.current) {
      setTimeout(() => {
        ref?.current?.focus();
      }, 500);
    }
  }, [ref.current]);

  const getInputField = (field) => {
    switch (type) {
      case "password":
        return <AntdInput.Password {...field} />;

      case "textarea":
        return <AntdInput.TextArea {...field} rows={4} />;
      default:
        return (
          <AntdInput
            {...field}
            disabled={disabled}
            autoComplete="off"
            ref={ref}
          />
        );
    }
  };

  return (
    <FormItem horizontal={horizontal}>
      {label && <label>{t(label)}</label>}
      {control ? (
        <Controller
          render={({ field }) => getInputField(field)}
          name={name}
          defaultValue={defaultValue || ""}
          control={control}
        />
      ) : (
        getInputField({})
      )}
      {error && <Error>{t(error)}</Error>}
    </FormItem>
  );
};

const Input = withErrorBoundary(React.memo(InputBase), "error.generic");

export { Input };

export const FormItem = styled.div<{ horizontal?: boolean }>`
  margin-bottom: 18px;
  display: ${({ horizontal }) => (horizontal ? "flex" : "block")};
  gap: ${({ horizontal }) => (horizontal ? "10px" : "0px")};
  align-items: ${({ horizontal }) => (horizontal ? "center" : "normal")};
  & > label {
    display: block;
    flex: ${({ horizontal }) => (horizontal ? "1" : "none")};
    margin-bottom: ${({ horizontal }) => (horizontal ? "0px" : "6px")};
    font-weight: 400;
    font-size: 13px;
  }
  & .ant-input {
    min-height: 36px;
    flex: ${({ horizontal }) => (horizontal ? "4" : "unset")};
  }
  & .ant-input-password {
    padding: 0px 16px;
  }
  & .ant-select-selector {
    min-height: 36px !important;
  }
  & .ant-select-selection-item {
    line-height: 34px !important;
  }

  & .p-password-input {
    width: 100%;
    box-shadow: none;
    padding: 0px 16px;
    min-height: 36px;
  }

  & .p-password {
    width: 100%;
  }
`;

export const Error = styled.p`
  color: ${theme.required};
  margin-top: 3px;
`;
