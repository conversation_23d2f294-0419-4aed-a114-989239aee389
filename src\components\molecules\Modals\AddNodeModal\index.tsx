import { styled } from "@linaria/react";
import { Button, notification } from "antd";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Input, Select } from "../../../atoms";
import { useMutation, useQueryClient } from "react-query";
import {
  addAttributes,
  addNodeService,
  editNodeService,
} from "../../../../services/node";
import React, { useEffect, useMemo, useState } from "react";
import { checkForUniqueness } from "../../../../utils";
import { createObjectTemplate } from "../../../../services";
import {
  GET_ALL_TEMPLATES_KEY,
  GET_CHILDRENS,
  GET_COUNTERS,
  GET_HISTORY_DATA,
  GET_NODE_ATTRIBUTES_DETAILS,
  OBJECT_TEMPLATE_ID,
  TEMPLATES_ATTRIBUTE_TEMPLATE_ID,
  TEMP_GROUPING_NODE_ID,
  getAttributeIcon,
} from "../../../../constants";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import {
  setDisplaySaveButton,
  setExpandedKeys,
  setMask,
} from "../../../../store/features/sidebar";
import { useTemplateActions } from "../../../../utils/functions/customHooks";
import { DELETED_FLAG, ITemplates } from "../../../../interfaces";
import { setRefreshBreadcrumbs } from "../../../../store/features";
import { Dialog } from "primereact/dialog";
import { MultiSelect } from "primereact/multiselect";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

import {
  setBottomNavbarOpen,
  setSelectedBottomNavbar,
} from "../../../../store/features/navigation";

interface Props {
  onClose: () => void;
  isOpen: boolean;
  edit?: boolean;
  id: number;
  label?: string;
  isAsset?: boolean;
  // eslint-disable-next-line no-unused-vars
  afterSave?: (id, name, icon, allowedChildren?: any) => void;
  nodeType: 3 | 4;
  // eslint-disable-next-line no-unused-vars
  handleSave?: any;
  fromMenuCreator?: boolean;
  title?: string;
  treeData?: any[];
  parentId?: number;
  fromHeader?: boolean;
  metamodel?: boolean;
  templateIds?: any;
  updateAllowedChildrens?: any;
  // editAllowedChildren?: boolean;
  setTreeData?: any;
  setAction?: any;
  setDropdownOpen?: any;
}

type FormData = {
  name: string;
  template: any[];
  allowedChildrens?: any[];
};

const AddNodeModalBase = ({
  onClose,
  isOpen,
  metamodel,
  edit,
  id,
  label,
  afterSave,
  nodeType,
  fromMenuCreator,
  title,
  handleSave,
  templateIds,
  treeData,
  parentId,
  fromHeader,
  isAsset,
  setTreeData,
}: Props) => {
  const { t } = useTranslation();
  const params = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [templateOptions, setTemplateOptions] = useState([]);
  const [allowedChildrenOptions, setAllowedChildrenOptions] = useState([]);

  const { getTemplateName, getAllowedChildrens } = useTemplateActions();

  const expandedKeys = useSelector(
    (state: RootState) => state.sidebar.expandedKeys
  );

  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const parentBreadcrumbs = useSelector(
    (state: RootState) => state.breadcrumbs.parentBreadcrumbs
  );

  const templatesDataArray = queryClient.getQueryData(
    GET_ALL_TEMPLATES_KEY
  ) as ITemplates[];

  const bodyData = useMemo(() => {
    return queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      id.toString(),
    ]) as any;
  }, [id]);

  useEffect(() => {
    if (templateIds == TEMP_GROUPING_NODE_ID) {
      const allowedChildrens = [];

      if (
        id == Number(params?.nodeId) ||
        (parentId == Number(params?.nodeId) && edit)
      ) {
        const ID = parentId || id;
        const selectedParentBreadcrumb = parentBreadcrumbs?.find(
          (breadcrumb) => breadcrumb.id === ID
        );

        const parentAllowedChildrens =
          selectedParentBreadcrumb?.allowedChildrens;

        parentAllowedChildrens?.forEach((allowedChild) => {
          if (!allowedChild?.inTrash)
            allowedChildrens.push({
              label: allowedChild?.name,
              value: allowedChild?.id?.toString(),
            });
        });
      } else {
        const bodyData = queryClient.getQueryData([
          GET_NODE_ATTRIBUTES_DETAILS,
          id.toString(),
        ]) as any;
        const allowedChildren =
          bodyData?.body?.find((body) => body?.type === "allowedChildren")
            ?.value || [];

        allowedChildren?.forEach((allowedChild) => {
          allowedChildrens.push({
            label: allowedChild?.name,
            value: allowedChild?.id.toString(),
          });
        });
      }
      setAllowedChildrenOptions([...allowedChildrens]);
    }
  }, [parentBreadcrumbs, id, templateIds, params?.nodeId, parentId]);

  useEffect(() => {
    if (templatesData) {
      const templates = [];

      templatesDataArray?.forEach((item) => {
        if (item.nodeType === "DATA" && !item?.flag?.includes("DELETED"))
          templates.push({ label: item?.name, value: item?.id.toString() });
      });
      setTemplateOptions([...templates]);
    }
  }, [templatesData]);

  const workingVersionMutation = useMutation(createObjectTemplate, {
    onSuccess: (data: any) => {
      afterSave &&
        afterSave(data?.orginal?.id, getValues("name"), "_30_document");

      queryClient.invalidateQueries([GET_CHILDRENS, id.toString()]);

      reset();
      onClose();

      dispatch(setBottomNavbarOpen(true));
      dispatch(setSelectedBottomNavbar("working-version"));
      navigate(`/settings/metamodel/-1?nodeId=${data?.orginal?.id}`);
      setTimeout(() => {
        dispatch(setRefreshBreadcrumbs(true));
      }, 400);
    },
  });

  const updateBodyMutation = useMutation(addAttributes, {
    onSuccess: () => {
      queryClient.invalidateQueries([
        GET_NODE_ATTRIBUTES_DETAILS,
        id.toString(),
      ]);
    },
    onError: () => {
      notification.error({
        message: t("Error Occurred"),
        description: t("Please try again after sometime"),
      });
    },
  });

  const mutation = useMutation(edit ? editNodeService : addNodeService, {
    onSuccess: (data: any) => {
      // if (editAllowedChildren) {
      //   queryClient.invalidateQueries(["get-attributes", id.toString()]);
      //   updateAllowedChildrens(getValues("template"));
      //   reset();
      //   onClose();
      //   return;
      // }
      afterSave &&
        afterSave(
          data,
          getValues("name"),
          "_30_folder",
          getValues("allowedChildrens")
        );
      if (fromHeader) {
        if (!expandedKeys?.some((item: number) => item === id)) {
          dispatch(setExpandedKeys([...expandedKeys, id]));
        }
        const previousData = queryClient.getQueryData([
          GET_CHILDRENS,
          id.toString(),
        ]);

        if (!previousData && parentId) {
          queryClient.invalidateQueries([GET_CHILDRENS, parentId.toString()]);
        } else {
          const newNode = {
            key: data,
            icon: getAttributeIcon("_30_folder"),
            name: getValues("name"),
            parentId: Number(parentId),
            templateId: getValues("template") as any,
            isLeaf: true,
            children: null,
            allowedChildrens: [],
            breadcrumb: [
              { id: id, name: getValues("name"), parentId: parentId },
            ],
          };

          dispatch(setTreeData([newNode, ...treeData]));
        }
      }

      if (edit) {
        queryClient.invalidateQueries([GET_HISTORY_DATA, id.toString()]);
        queryClient.invalidateQueries([GET_COUNTERS, id.toString()]);
        if (templateIds === TEMP_GROUPING_NODE_ID) {
          const newBody = [...bodyData.body];
          const allowedChildrenIndex = newBody.find(
            (body) => body.type === "allowedChildren"
          );
          if (allowedChildrenIndex) {
            allowedChildrenIndex.value = getValues("allowedChildrens");
          } else {
            newBody.push({
              id: "-1",
              type: "allowedChildren",
              value: getValues("allowedChildrens"),
            });
          }
          updateBodyMutation.mutate({
            id: id,
            specialAttribute: false,
            body: newBody,
          });
        }
      }
      reset();
      onClose();
    },
    onError: (response: any) => {
      if (
        response?.data?.error &&
        response?.data?.error.includes("Sibling node name must be unique")
      ) {
        notification.error({
          message: t("Name not unique!"),
          description: t(
            `Node with name ${getValues(
              "name"
            )} already exists, please try again with different name!`
          ),
        });
      } else
        notification.error({
          message: t("Error Occurred"),
          description: t("Please try again after sometime"),
        });
    },
  });

  const assetValidationSchema = yup.object({
    template: yup.array(),
  });

  const validationSchema = yup.object({
    name: yup.string().required("Please enter"),
  });

  const {
    handleSubmit,
    control,
    trigger,
    reset,
    watch,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<FormData>({
    defaultValues: {
      template: [],
      allowedChildrens: [],
    },
    resolver: yupResolver(isAsset ? assetValidationSchema : validationSchema),
  });

  useEffect(() => {
    if (edit) {
      setValue("name", label);
    }
    setValue("template", templateIds);
  }, [id, templateIds, isAsset]);

  const onSubmit = async (value) => {
    if (isObjectTemplate && !edit) {
      const isUnique = checkForUniqueness(
        value?.name,
        value?.template,
        treeData,
        id,
        null,
        params?.nodeId
      );

      if (isUnique) {
        workingVersionMutation.mutate({
          parentId: id,
          name: value?.name.trim(),
        });
      } else {
        notification.error({
          message: t("Node with name already exists!"),
          description: t("Please try again with different name"),
        });
      }

      return;
    }

    if (!edit && !fromMenuCreator) {
      const isUnique = checkForUniqueness(
        value?.name,
        value?.template,
        treeData,
        id,
        null,
        params?.nodeId
      );
      if (isUnique) {
        const randomTemporaryID = Math.floor(Math.random() * 1000);
        let icon = "_30_folder";

        const selectedTemplate = templatesData[Number(templateIds)];

        if (selectedTemplate) {
          icon = selectedTemplate.icon;
        }

        afterSave &&
          afterSave(
            randomTemporaryID,
            getValues("name").trim(),
            icon,
            getValues("allowedChildrens")
          );

        if (fromHeader || isAttributeTemplate) {
          if (!expandedKeys?.some((item) => item === id)) {
            dispatch(setExpandedKeys([...expandedKeys, id]));
          }
          const previousData = queryClient.getQueryData([
            "get-nodes",
            id.toString(),
          ]);
          if (!previousData && parentId) {
            queryClient.invalidateQueries(["get-nodes", parentId.toString()]);
          } else {
            const newNode = {
              key: randomTemporaryID,
              name: getValues("name").trim(),
              title: getValues("name"),
              parentId: Number(parentId),
              templateId: getValues("template") as any,
              isLeaf: true,
              children: null,
              icon: getAttributeIcon("_30_folder"),
              allowedChildrens: [],
              breadcrumb: [
                {
                  id: id,
                  name: getValues("name").trim(),
                  parentId: parentId,
                  templateName: getTemplateName(getValues("template") as any),
                  templateId: getValues("template"),
                  allowedChildrens: getAllowedChildrens(
                    getValues("template") as any
                  ),
                },
              ],
            };

            setTreeData([newNode, ...treeData]);
          }
        }

        reset();
        onClose();

        if (!isAttributeTemplate) {
          const pathname =
            import.meta.env.VITE_APP_BASE_URL !== "/"
              ? location.pathname.replace(import.meta.env.VITE_APP_BASE_URL, "")
              : location.pathname;
          navigate(
            `${pathname}?nodeId=${randomTemporaryID}&draft=true&template=${selectedTemplate?.id}`
          );
          dispatch(setMask(true));
          dispatch(setDisplaySaveButton(true));
        }
      } else {
        notification.error({
          message: t("Node with name already exists!"),
          description: t("Please try again with different name"),
        });
      }

      return;
    }

    if (isAsset) {
      handleSave(value.template);
      reset();
      onClose();
    } else if (handleSave) {
      const isUnique = handleSave(value.name, value.template || templateIds);
      if (isUnique) {
        reset();
        onClose();
      }
    } else {
      if (fromHeader) {
        mutation.mutate(
          edit
            ? {
                id: id,
                oldName: label,
                newName: value.name,
              }
            : {
                name: value.name,
                id: id,
                type: nodeType,
                templateId: Number(value.template || templateIds),
                allowedChildren: Number(value.allowedChildren),
              }
        );
      } else {
        const isUnique = checkForUniqueness(
          value?.name,
          value.template,
          treeData,
          edit ? parentId : id,
          id,
          params?.nodeId
        );

        if (isUnique) {
          mutation.mutate(
            edit
              ? { id: id, oldName: label, newName: value.name }
              : {
                  name: value.name,
                  id: id,
                  type: nodeType,
                  templateId: value.template || templateIds,
                  nodeType: "DATA",
                }
          );
        } else {
          notification.error({
            message: t("Node with name already exists!"),
            description: t("Please try again with different name"),
          });
        }
      }
    }
  };

  const isObjectTemplate = templateIds == OBJECT_TEMPLATE_ID;
  const isAttributeTemplate = templateIds == TEMPLATES_ATTRIBUTE_TEMPLATE_ID;

  useEffect(() => {
    if (edit) {
      const bodyData = queryClient.getQueryData([
        GET_NODE_ATTRIBUTES_DETAILS,
        id.toString(),
      ]) as any;

      const allowedChildren = bodyData?.body?.find(
        (body) => body?.type === "allowedChildren"
      )?.value;
      if (getValues("allowedChildrens").length === 0)
        setValue("allowedChildrens", allowedChildren || []);
    }
  }, [edit, id]);

  return (
    <Dialog
      visible={isOpen}
      onHide={onClose}
      footer={null}
      className="export-modal draggable-modal"
      header={title || (edit ? t("Rename") : t("Add folder"))}
    >
      <Wrapper isAsset={isAsset} onSubmit={handleSubmit(onSubmit)}>
        {isAsset ? (
          <Row>
            <h6>{t("Allowed children")}</h6>

            <Select
              multiple
              error={errors.template?.message}
              name="template"
              control={control}
              options={templateOptions?.map((item) => {
                return { ...item, title: null };
              })}
              value={getValues("template")?.map((temp) => ({
                value: temp.id.toString(),
                label: temp.name,
              }))}
              onChange={(values) => {
                if (metamodel) {
                  setValue("template", values);
                } else {
                  const selectedValues = [];
                  values?.forEach((template) => {
                    const selectedTemplate = templatesData[Number(template)];
                    selectedValues.push({
                      id: selectedTemplate?.id,
                      name: selectedTemplate?.name,
                      inTrash: selectedTemplate?.flag.includes(DELETED_FLAG),
                    });
                  });

                  setValue("template", selectedValues);
                }
                trigger("template");
              }}
            />
            <Button
              htmlType="submit"
              type="primary"
              loading={mutation.isLoading}
            >
              {edit ? t("Update") : t("Save")}
            </Button>
          </Row>
        ) : (
          <>
            <Row>
              <h6>{t("Name")}</h6>
              <Input
                error={errors.name?.message}
                name="name"
                control={control}
              />
            </Row>
            {/* hard coded */}
            {templateIds == TEMP_GROUPING_NODE_ID && (
              <Row>
                <h6>{t("Allowed Children")}</h6>
                <section
                  id="multiselect-wrapper"
                  style={{ position: "relative", overflow: "auto" }}
                >
                  <MultiSelect
                    options={allowedChildrenOptions}
                    panelClassName="grouping-allowed-children"
                    value={watch("allowedChildrens")?.map((temp) =>
                      temp.id?.toString()
                    )}
                    filter
                    appendTo={document.getElementById("multiselect-wrapper")}
                    onChange={(event: any) => {
                      const allowedChildres = [];
                      event.target.value?.forEach((templateId) => {
                        const selectedTemplate =
                          templatesData[Number(templateId)];
                        allowedChildres.push({
                          id: templateId,
                          name: selectedTemplate?.name,
                          inTrash:
                            selectedTemplate?.flag?.includes(DELETED_FLAG),
                        });
                      });
                      setValue("allowedChildrens", allowedChildres);
                      trigger("allowedChildrens");
                    }}
                  />
                </section>
              </Row>
            )}
            <Button
              htmlType="submit"
              type="primary"
              disabled={
                templateIds == TEMP_GROUPING_NODE_ID
                  ? !watch("name")?.trim() ||
                    watch("allowedChildrens").length === 0
                  : !watch("name")?.trim()
              }
              loading={mutation.isLoading || workingVersionMutation.isLoading}
            >
              {edit ? t("Update") : t("Add")}
            </Button>
          </>
        )}
      </Wrapper>
    </Dialog>
  );
};

const AddNodeModal = withErrorBoundary(
  React.memo(AddNodeModalBase),
  "error.generic"
);

export { AddNodeModal };

const Row = styled.div`
  display: flex;
  border: 0.5px solid #eaeaea;
  border-radius: 4px;
  margin-bottom: 10px;

  & > button {
    margin-top: 10px;
    margin-right: 10px;
  }
  & .ant-select {
    width: 100%;
  }
  & h6 {
    font-size: 13px;
    font-weight: 400;
    background-color: var(--color-light);
    border-right: 1px solid #eaeaea;
    flex: 1;
    align-items: center;
    color: var(--color-text);
    padding: 6px 10px;
    display: flex;
  }

  & > div,
  > section {
    padding: 8px 10px;
    flex: 2;
    margin-bottom: 0px;

    & .p-multiselect {
      width: 100%;
    }
  }
`;

const Wrapper = styled.form<{ isAsset: boolean }>`
  padding-bottom: ${({ isAsset }) => (isAsset ? "40px" : "20px")};
  & > button {
    margin-left: auto;
    display: flex;
    margin-top: 20px;
    font-size: 13px;
  }

  & button:disabled {
    background-color: var(--color-text);
    color: #fff;
    opacity: 0.7;
  }
  & .ant-select-selection-item {
    display: flex;
    align-items: center;
    color: #094375;
  }
`;
