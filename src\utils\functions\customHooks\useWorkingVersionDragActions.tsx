import { checkForUniqueness } from "../checkForUniqueness";
import { notification } from "antd";
import { moveNode } from "../../../services/node";
import { searchRecursivelyByKey } from "../recursives";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useWorkingVersionDragActions = () => {
  const { t } = useTranslation();
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const loop = (data, key, callback) => {
    data.forEach((item, index, arr) => {
      if (item.key === key) {
        return callback({ ...item }, index, arr);
      }
      if (item.children) {
        return loop(item.children, key, callback);
      }
    });
  };

  const handleWorkingVersionDrop = async (info: any, data, setTreeData) => {
    const treeData = [...data];
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split("-");
    const dropPosition =
      info.dropPosition - Number(dropPos[dropPos.length - 1]);
    const dropKey = info.node.key;

    try {
      let newParent = null;
      let newParentId = null;
      if (!info.dropToGap) {
        newParent = info.node;
        newParentId = info.node.key;
      } else {
        newParentId = info.node.parentId;
        newParent = searchRecursivelyByKey(treeData, info.node.parentId, "key");
        newParentId = info.node.parentId;
      }

      if (!newParent) {
        notification.error({
          message: t("Operation restricted!"),
          description: t("You can't add atribute template in a root level."),
        });
        return;
      }

      if (info.dragNode.parentId !== newParentId) {
        const isUnique = checkForUniqueness(
          info.dragNode?.name,
          info.dragNode?.templateId,
          treeData,
          newParentId
        );

        if (!isUnique) {
          notification.error({
            message: t("Node with name already exists!"),
            description: t("Please try again with different name"),
          });
          return;
        }

        let parentAllowedChildrens = null;
        let parentTemplate = null;

        const parentTemplateId = newParent?.templateId;
        parentTemplate = templatesData[Number(parentTemplateId)];
        parentAllowedChildrens = parentTemplate.allowedChildren;

        const childTemplateType =
          templatesData[Number(info.dragNode.templateId)];

        if (!(info.dragNode.templateId in parentAllowedChildrens)) {
          notification.error({
            message: t("Move operation restricted!"),
            description:
              Object.keys(parentAllowedChildrens).length === 0
                ? t("DRAG_RESTRICTED_NO_ALLOWED_CHILDREN", {
                    name: parentTemplate?.name,
                  })
                : t("DRAG_RESTRICTED_TYPE_MISMATCHED", {
                    parent: parentTemplate?.name,
                    child: childTemplateType?.name,
                  }),
          });
          return;
        }
      }

      await moveNode(
        dragKey,
        info.dragNode.parentId,
        newParentId,
        dropPosition === -1 || !info.dropToGap ? -1 : info.node.key
      );

      let dragObj;

      loop(treeData, dragKey, (item, index, arr) => {
        arr.splice(index, 1);
        dragObj = item;
      });

      if (!info.dropToGap) {
        // Drop on the content, add to first
        loop(treeData, dropKey, (item: any) => {
          dragObj.parentId = info.node.key;
          item.children = item.children || [];
          item.children.unshift(dragObj);
          item.isLeaf = item.children.length === 0;
        });
      } else {
        // above some target node
        dragObj.parentId = info.node.parentId;
        let ar: any[] = [];
        let i: number;
        loop(treeData, dropKey, (_item, index, arr) => {
          ar = arr;
          i = index;
        });

        if (dropPosition === -1) {
          // above
          ar.splice(i, 0, dragObj!);
        } else {
          // below
          ar.splice(i + 1, 0, dragObj!);
        }
      }

      const oldParent = searchRecursivelyByKey(
        treeData,
        info.dragNode.parentId,
        "key"
      );

      if (oldParent && oldParent.children && oldParent.children.length === 0) {
        oldParent.isLeaf = true;
      }

      const newParentNode = searchRecursivelyByKey(
        treeData,
        newParentId,
        "key"
      );

      if (
        newParentNode &&
        newParentNode.children &&
        newParentNode.children.length > 0 &&
        newParentNode.isLeaf
      ) {
        newParentNode.isLeaf = false;
      }

      setTreeData([...treeData]);
      // const newParentData = await getAllNodes(newParentId, true);
      // queryClient.setQueryData(
      //   [GET_CHILDRENS, newParentId.toString()],
      //   newParentData
      // );
      // if (info.dragNode.parentId !== newParentId) {
      //   const newParentData = await getAllNodes(info.dragNode.parentId, true);
      //   queryClient.setQueryData(
      //     [GET_CHILDRENS, info.dragNode.parentId.toString()],
      //     newParentData
      //   );
      // }
    } catch (e) {
      notification.open({
        message: t("Error while moving"),
        type: "error",
        description: t("Please try again!"),
      });
    }
  };
  return { handleWorkingVersionDrop };
};

export { useWorkingVersionDragActions };
