/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

.font-size-input {
  font-weight: bold;
  font-size: 14px;
  color: #777;
  border-radius: 5px;
  border-color: grey;
  height: 15px;
  padding: 2px 4px;
  text-align: center;
  width: 20px;
  align-self: center;
}

.font-size-input:disabled {
  opacity: 0.2;
  cursor: not-allowed;
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

.add-icon {
  background-image: url(../../images/icons/add-sign.svg);
  background-repeat: no-repeat;
  background-position: center;
}

.minus-icon {
  background-image: url(../../images/icons/minus-sign.svg);
  background-repeat: no-repeat;
  background-position: center;
}

button.font-decrement {
  padding: 0px;
  margin-right: 3px;
}

button.font-increment {
  padding: 0px;
  margin-left: 3px;
}
